@import "tailwindcss";

:root {
  /* Default to dark theme colors as black is primary */
  --background: #000000; /* Black */
  --foreground: #FFFFFF; /* White (for text on black bg) */
  --brand-purple: #800080; /* Purple */
  --brand-yellow: #FFD700; /* Yellow (Gold-ish) */
  --brand-black: #000000; /* Black */
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

/* We can remove the light mode override if the site is primarily dark themed,
   or adjust it if you want a light version with this new palette. 
   For now, I'll keep it but make it consistent with the new dark theme base.
*/
@media (prefers-color-scheme: dark) {
  :root {
    --background: #000000; /* Black */
    --foreground: #FFFFFF; /* White */
  }
}

/* You might want a slightly off-black for the main body if pure black is too stark */
body {
  background: var(--background); /* Or a slightly lighter black like #121212 */
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
}
