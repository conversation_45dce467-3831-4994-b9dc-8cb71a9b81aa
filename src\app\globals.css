@import "tailwindcss";

:root {
  /* Enhanced Professional Color System */
  --background: #0f0f23; /* Deep navy-black for sophistication */
  --foreground: #f8fafc; /* Clean white for optimal readability */
  --background-secondary: #1e293b; /* Secondary dark background */
  --background-tertiary: #334155; /* Tertiary background for cards */

  /* Brand Colors */
  --brand-primary: #6366f1; /* Modern indigo */
  --brand-secondary: #f59e0b; /* Warm amber */
  --brand-accent: #8b5cf6; /* Purple accent */

  /* Semantic Colors */
  --success: #22c55e;
  --warning: #f59e0b;
  --error: #ef4444;
  --info: #3b82f6;

  /* Glass Effect Variables */
  --glass-bg: rgba(248, 250, 252, 0.05);
  --glass-border: rgba(248, 250, 252, 0.1);
  --glass-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);

  /* Animation Variables */
  --transition-fast: 0.15s ease-out;
  --transition-normal: 0.3s ease-out;
  --transition-slow: 0.5s ease-out;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

/* Enhanced Body Styling */
body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-geist-sans), 'Inter', system-ui, sans-serif;
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  scroll-behavior: smooth;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--background-secondary);
}

::-webkit-scrollbar-thumb {
  background: var(--brand-primary);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--brand-accent);
}

/* Glass Morphism Utility Classes */
.glass {
  background: var(--glass-bg);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid var(--glass-border);
  box-shadow: var(--glass-shadow);
}

.glass-card {
  @apply glass rounded-xl p-6 transition-all duration-300;
}

.glass-card:hover {
  background: rgba(248, 250, 252, 0.08);
  transform: translateY(-2px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.4);
}

/* Enhanced Button Styles */
.btn-primary {
  @apply bg-gradient-to-r from-brand-primary to-brand-accent text-white font-semibold py-3 px-6 rounded-lg transition-all duration-300 shadow-lg hover:shadow-glow transform hover:scale-105 active:scale-95;
}

.btn-secondary {
  @apply bg-gradient-to-r from-brand-secondary to-yellow-500 text-neutral-900 font-semibold py-3 px-6 rounded-lg transition-all duration-300 shadow-lg hover:shadow-glow-amber transform hover:scale-105 active:scale-95;
}

.btn-outline {
  @apply border-2 border-brand-primary text-brand-primary font-semibold py-3 px-6 rounded-lg transition-all duration-300 hover:bg-brand-primary hover:text-white transform hover:scale-105 active:scale-95;
}

.btn-ghost {
  @apply text-neutral-300 font-semibold py-3 px-6 rounded-lg transition-all duration-300 hover:bg-neutral-800 hover:text-white transform hover:scale-105 active:scale-95;
}

/* Enhanced Typography */
.heading-xl {
  @apply text-5xl md:text-6xl lg:text-7xl font-bold leading-tight;
}

.heading-lg {
  @apply text-4xl md:text-5xl font-bold leading-tight;
}

.heading-md {
  @apply text-3xl md:text-4xl font-semibold leading-tight;
}

.heading-sm {
  @apply text-2xl md:text-3xl font-semibold leading-tight;
}

.text-gradient-brand {
  @apply bg-gradient-to-r from-brand-primary via-brand-accent to-brand-secondary bg-clip-text text-transparent;
}

.text-gradient-primary {
  @apply bg-gradient-to-r from-brand-primary to-brand-accent bg-clip-text text-transparent;
}

.text-gradient-secondary {
  @apply bg-gradient-to-r from-brand-secondary to-yellow-400 bg-clip-text text-transparent;
}

/* Enhanced Card Styles */
.card {
  @apply bg-neutral-800/50 backdrop-blur-sm border border-neutral-700/50 rounded-xl p-6 transition-all duration-300 hover:bg-neutral-800/70 hover:border-neutral-600/50 hover:shadow-large;
}

.card-elevated {
  @apply card shadow-medium hover:shadow-large hover:-translate-y-1;
}

.card-interactive {
  @apply card-elevated cursor-pointer hover:scale-[1.02] active:scale-[0.98];
}

/* Focus States */
.focus-ring {
  @apply focus:outline-none focus:ring-2 focus:ring-brand-primary focus:ring-offset-2 focus:ring-offset-neutral-900;
}

/* Loading States */
.loading-shimmer {
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
  0% { background-position: -200% 0; }
  100% { background-position: 200% 0; }
}

/* Responsive Text */
@media (max-width: 640px) {
  .heading-xl { @apply text-4xl; }
  .heading-lg { @apply text-3xl; }
  .heading-md { @apply text-2xl; }
  .heading-sm { @apply text-xl; }
}
