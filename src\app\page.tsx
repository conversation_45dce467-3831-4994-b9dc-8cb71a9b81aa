"use client";

import Link from 'next/link';
import Image from 'next/image';
import { FaChevronDown, FaPlayCircle, FaEnvelope, FaStar, FaVideo, FaAward } from 'react-icons/fa';
import { useEffect, useRef } from 'react';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';

// Register GSAP plugins
if (typeof window !== 'undefined') {
  gsap.registerPlugin(ScrollTrigger);
}

export default function Home() {
  const heroRef = useRef<HTMLDivElement>(null);
  const imageRef = useRef<HTMLDivElement>(null);
  const titleRef = useRef<HTMLHeadingElement>(null);
  const subtitleRef = useRef<HTMLHeadingElement>(null);
  const descriptionRef = useRef<HTMLParagraphElement>(null);
  const buttonsRef = useRef<HTMLDivElement>(null);
  const chevronRef = useRef<HTMLDivElement>(null);
  const contentRef = useRef<HTMLDivElement>(null);
  const statsRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const ctx = gsap.context(() => {
      // Hero entrance animations
      const tl = gsap.timeline();
      
      // Image entrance with scale and rotation
      tl.fromTo(imageRef.current, 
        { scale: 0, rotation: -180, opacity: 0 },
        { scale: 1, rotation: 0, opacity: 1, duration: 1.2, ease: "back.out(1.7)" }
      )
      // Title animation with split text effect
      .fromTo(titleRef.current?.children || [], 
        { y: 100, opacity: 0, rotationX: 90 },
        { y: 0, opacity: 1, rotationX: 0, duration: 0.8, stagger: 0.1, ease: "power3.out" },
        "-=0.5"
      )
      // Subtitle with typewriter effect
      .fromTo(subtitleRef.current, 
        { width: 0, opacity: 0 },
        { width: "auto", opacity: 1, duration: 1, ease: "power2.out" },
        "-=0.3"
      )
      // Description fade up
      .fromTo(descriptionRef.current, 
        { y: 50, opacity: 0 },
        { y: 0, opacity: 1, duration: 0.8, ease: "power2.out" },
        "-=0.5"
      )
      // Buttons with bounce effect
      .fromTo(buttonsRef.current?.children || [], 
        { y: 30, opacity: 0, scale: 0.8 },
        { y: 0, opacity: 1, scale: 1, duration: 0.6, stagger: 0.2, ease: "back.out(1.7)" },
        "-=0.3"
      )
      // Chevron floating animation
      .fromTo(chevronRef.current, 
        { y: 20, opacity: 0 },
        { y: 0, opacity: 1, duration: 0.5, ease: "power2.out" },
        "-=0.2"
      );

      // Continuous floating animation for image
      gsap.to(imageRef.current, {
        y: -10,
        duration: 2,
        repeat: -1,
        yoyo: true,
        ease: "power2.inOut"
      });

      // Continuous floating animation for chevron
      gsap.to(chevronRef.current, {
        y: 10,
        duration: 1.5,
        repeat: -1,
        yoyo: true,
        ease: "power2.inOut"
      });

      // Scroll-triggered animations for content section
      gsap.fromTo(contentRef.current?.children || [], 
        { y: 80, opacity: 0, scale: 0.9 },
        {
          y: 0,
          opacity: 1,
          scale: 1,
          duration: 1,
          stagger: 0.2,
          ease: "power3.out",
          scrollTrigger: {
            trigger: contentRef.current,
            start: "top 80%",
            end: "bottom 20%",
            toggleActions: "play none none reverse"
          }
        }
      );

      // Stats counter animation
      gsap.fromTo(statsRef.current?.children || [], 
        { scale: 0, rotation: 180 },
        {
          scale: 1,
          rotation: 0,
          duration: 0.8,
          stagger: 0.1,
          ease: "back.out(1.7)",
          scrollTrigger: {
            trigger: statsRef.current,
            start: "top 80%",
            toggleActions: "play none none reverse"
          }
        }
      );

      // Parallax effect for background
      gsap.to(heroRef.current, {
        backgroundPosition: "50% 100px",
        ease: "none",
        scrollTrigger: {
          trigger: heroRef.current,
          start: "top bottom",
          end: "bottom top",
          scrub: true
        }
      });

    }, heroRef);

    return () => ctx.revert();
  }, []);

  // Mouse move parallax effect
  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      const { clientX, clientY } = e;
      const { innerWidth, innerHeight } = window;
      
      const xPos = (clientX / innerWidth - 0.5) * 20;
      const yPos = (clientY / innerHeight - 0.5) * 20;
      
      gsap.to(imageRef.current, {
        x: xPos,
        y: yPos,
        duration: 0.5,
        ease: "power2.out"
      });
    };

    window.addEventListener('mousemove', handleMouseMove);
    return () => window.removeEventListener('mousemove', handleMouseMove);
  }, []);

  return (
    <div className="bg-black text-white overflow-hidden">
      {/* Hero Section */}
      <section ref={heroRef} className="min-h-screen flex flex-col justify-center items-center text-center p-8 relative">
        {/* Enhanced animated background */}
        <div className="absolute inset-0 bg-mesh-gradient opacity-30">
          <div className="absolute top-1/4 left-1/4 w-3 h-3 bg-brand-primary rounded-full opacity-60 animate-float"></div>
          <div className="absolute top-3/4 right-1/4 w-2 h-2 bg-brand-secondary rounded-full opacity-80 animate-ping"></div>
          <div className="absolute top-1/2 left-3/4 w-4 h-4 bg-brand-accent rounded-full opacity-50 animate-bounce"></div>
          <div className="absolute bottom-1/4 right-1/2 w-2.5 h-2.5 bg-brand-primary rounded-full opacity-70 animate-pulse"></div>
        </div>
        
        <div className="relative z-10 flex flex-col items-center">
          <div ref={imageRef} className="mb-8">
            <Image 
              src="/placeholder-headshot.svg"
              alt="Video Editor Headshot"
              width={128}
              height={128}
              className="rounded-full border-4 border-blue-500 shadow-2xl hover:border-brand-yellow transition-colors duration-300"
            />
          </div>
          
          <h1 ref={titleRef} className="text-5xl md:text-7xl font-bold mb-6 bg-gradient-to-r from-white via-indigo-300 to-purple-400 bg-clip-text text-transparent">
            <span className="inline-block">Masterful</span>{' '}
            <span className="inline-block">Video</span>{' '}
            <span className="inline-block">Editing</span>
          </h1>

          <h2 ref={subtitleRef} className="text-4xl md:text-6xl font-bold mb-8 bg-gradient-to-r from-amber-400 to-orange-500 bg-clip-text text-transparent overflow-hidden whitespace-nowrap">
            for Impact
          </h2>

          <p ref={descriptionRef} className="text-lg md:text-xl text-gray-300 mb-10 max-w-xl leading-relaxed">
            I transform raw footage into polished, compelling narratives that captivate your audience and elevate your brand. Your vision, expertly edited.
          </p>
          
          <div ref={buttonsRef} className="flex flex-col sm:flex-row items-center space-y-4 sm:space-y-0 sm:space-x-6 mb-16">
            <Link href="/portfolio" className="group bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 text-white font-semibold py-4 px-10 rounded-lg text-lg flex items-center justify-center w-full sm:w-auto transform hover:scale-105 transition-all duration-300 shadow-lg hover:shadow-xl">
              <FaPlayCircle className="mr-3 group-hover:animate-spin" /> View My Work
            </Link>
            <Link href="/contact" className="group border-2 border-indigo-500 text-indigo-400 hover:bg-indigo-500 hover:text-white font-semibold py-4 px-10 rounded-lg text-lg flex items-center justify-center w-full sm:w-auto transform hover:scale-105 transition-all duration-300">
              <FaEnvelope className="mr-3 group-hover:animate-bounce" /> Get in Touch
            </Link>
          </div>
          
          <div ref={chevronRef}>
            <FaChevronDown className="text-3xl text-gray-400 cursor-pointer hover:text-brand-yellow transition-colors duration-300" />
          </div>
        </div>
      </section>

      {/* Enhanced Content Section */}
      <section className="py-24 bg-gradient-to-b from-gray-900 to-black text-center relative">
        <div className="container mx-auto px-6">
          <div ref={contentRef}>
            <h2 className="text-4xl md:text-6xl font-bold mb-10 bg-gradient-to-r from-indigo-400 via-purple-400 to-amber-400 bg-clip-text text-transparent">
              Why Choose Studio Noir?
            </h2>
            <p className="text-lg md:text-xl text-gray-400 mb-16 max-w-3xl mx-auto leading-relaxed">
              Experience the difference of professional video editing that brings your vision to life with cinematic quality and creative excellence.
            </p>
            
            {/* Enhanced Stats Section */}
            <div ref={statsRef} className="grid grid-cols-1 md:grid-cols-3 gap-10 mb-20">
              <div className="bg-gray-800/50 backdrop-blur-sm border border-gray-700/50 rounded-xl p-10 text-center group hover:bg-gray-800/70 hover:border-gray-600/50 hover:shadow-2xl hover:-translate-y-2 transition-all duration-300">
                <FaVideo className="text-5xl text-indigo-400 mb-6 mx-auto group-hover:scale-110 transition-transform duration-300" />
                <h3 className="text-4xl font-bold text-white mb-3">500+</h3>
                <p className="text-gray-400 text-lg">Projects Completed</p>
              </div>
              <div className="bg-gray-800/50 backdrop-blur-sm border border-gray-700/50 rounded-xl p-10 text-center group hover:bg-gray-800/70 hover:border-gray-600/50 hover:shadow-2xl hover:-translate-y-2 transition-all duration-300">
                <FaStar className="text-5xl text-amber-400 mb-6 mx-auto group-hover:scale-110 transition-transform duration-300" />
                <h3 className="text-4xl font-bold text-white mb-3">5.0</h3>
                <p className="text-gray-400 text-lg">Average Rating</p>
              </div>
              <div className="bg-gray-800/50 backdrop-blur-sm border border-gray-700/50 rounded-xl p-10 text-center group hover:bg-gray-800/70 hover:border-gray-600/50 hover:shadow-2xl hover:-translate-y-2 transition-all duration-300">
                <FaAward className="text-5xl text-purple-400 mb-6 mx-auto group-hover:scale-110 transition-transform duration-300" />
                <h3 className="text-4xl font-bold text-white mb-3">50+</h3>
                <p className="text-gray-400 text-lg">Awards Won</p>
              </div>
            </div>
          </div>
        </div>

        {/* Enhanced floating background elements */}
        <div className="absolute top-16 left-16 w-24 h-24 bg-brand-primary/10 rounded-full blur-2xl animate-float"></div>
        <div className="absolute bottom-16 right-16 w-40 h-40 bg-brand-secondary/10 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute top-1/2 right-20 w-16 h-16 bg-brand-accent/10 rounded-full blur-xl animate-bounce"></div>
      </section>
    </div>
  );
}
