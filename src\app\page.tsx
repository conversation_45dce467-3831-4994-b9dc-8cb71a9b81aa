"use client";

import Link from 'next/link';
import Image from 'next/image';
import { FaChevronDown, FaPlayCircle, FaEnvelope, FaStar, FaVideo, FaAward } from 'react-icons/fa';
import { useEffect, useRef } from 'react';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';

// Register GSAP plugins
if (typeof window !== 'undefined') {
  gsap.registerPlugin(ScrollTrigger);
}

export default function Home() {
  const heroRef = useRef<HTMLDivElement>(null);
  const imageRef = useRef<HTMLDivElement>(null);
  const titleRef = useRef<HTMLHeadingElement>(null);
  const subtitleRef = useRef<HTMLHeadingElement>(null);
  const descriptionRef = useRef<HTMLParagraphElement>(null);
  const buttonsRef = useRef<HTMLDivElement>(null);
  const chevronRef = useRef<HTMLDivElement>(null);
  const contentRef = useRef<HTMLDivElement>(null);
  const statsRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const ctx = gsap.context(() => {
      // Hero entrance animations
      const tl = gsap.timeline();
      
      // Image entrance with scale and rotation
      tl.fromTo(imageRef.current, 
        { scale: 0, rotation: -180, opacity: 0 },
        { scale: 1, rotation: 0, opacity: 1, duration: 1.2, ease: "back.out(1.7)" }
      )
      // Title animation with split text effect
      .fromTo(titleRef.current?.children || [], 
        { y: 100, opacity: 0, rotationX: 90 },
        { y: 0, opacity: 1, rotationX: 0, duration: 0.8, stagger: 0.1, ease: "power3.out" },
        "-=0.5"
      )
      // Subtitle with typewriter effect
      .fromTo(subtitleRef.current, 
        { width: 0, opacity: 0 },
        { width: "auto", opacity: 1, duration: 1, ease: "power2.out" },
        "-=0.3"
      )
      // Description fade up
      .fromTo(descriptionRef.current, 
        { y: 50, opacity: 0 },
        { y: 0, opacity: 1, duration: 0.8, ease: "power2.out" },
        "-=0.5"
      )
      // Buttons with bounce effect
      .fromTo(buttonsRef.current?.children || [], 
        { y: 30, opacity: 0, scale: 0.8 },
        { y: 0, opacity: 1, scale: 1, duration: 0.6, stagger: 0.2, ease: "back.out(1.7)" },
        "-=0.3"
      )
      // Chevron floating animation
      .fromTo(chevronRef.current, 
        { y: 20, opacity: 0 },
        { y: 0, opacity: 1, duration: 0.5, ease: "power2.out" },
        "-=0.2"
      );

      // Continuous floating animation for image
      gsap.to(imageRef.current, {
        y: -10,
        duration: 2,
        repeat: -1,
        yoyo: true,
        ease: "power2.inOut"
      });

      // Continuous floating animation for chevron
      gsap.to(chevronRef.current, {
        y: 10,
        duration: 1.5,
        repeat: -1,
        yoyo: true,
        ease: "power2.inOut"
      });

      // Scroll-triggered animations for content section
      gsap.fromTo(contentRef.current?.children || [], 
        { y: 80, opacity: 0, scale: 0.9 },
        {
          y: 0,
          opacity: 1,
          scale: 1,
          duration: 1,
          stagger: 0.2,
          ease: "power3.out",
          scrollTrigger: {
            trigger: contentRef.current,
            start: "top 80%",
            end: "bottom 20%",
            toggleActions: "play none none reverse"
          }
        }
      );

      // Stats counter animation
      gsap.fromTo(statsRef.current?.children || [], 
        { scale: 0, rotation: 180 },
        {
          scale: 1,
          rotation: 0,
          duration: 0.8,
          stagger: 0.1,
          ease: "back.out(1.7)",
          scrollTrigger: {
            trigger: statsRef.current,
            start: "top 80%",
            toggleActions: "play none none reverse"
          }
        }
      );

      // Parallax effect for background
      gsap.to(heroRef.current, {
        backgroundPosition: "50% 100px",
        ease: "none",
        scrollTrigger: {
          trigger: heroRef.current,
          start: "top bottom",
          end: "bottom top",
          scrub: true
        }
      });

    }, heroRef);

    return () => ctx.revert();
  }, []);

  // Mouse move parallax effect
  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      const { clientX, clientY } = e;
      const { innerWidth, innerHeight } = window;
      
      const xPos = (clientX / innerWidth - 0.5) * 20;
      const yPos = (clientY / innerHeight - 0.5) * 20;
      
      gsap.to(imageRef.current, {
        x: xPos,
        y: yPos,
        duration: 0.5,
        ease: "power2.out"
      });
    };

    window.addEventListener('mousemove', handleMouseMove);
    return () => window.removeEventListener('mousemove', handleMouseMove);
  }, []);

  return (
    <div className="bg-black text-white overflow-hidden">
      {/* Hero Section */}
      <section ref={heroRef} className="min-h-screen flex flex-col justify-center items-center text-center p-8 relative">
        {/* Animated background particles */}
        <div className="absolute inset-0 bg-black">
          <div className="absolute top-1/4 left-1/4 w-2 h-2 bg-brand-purple rounded-full opacity-50 animate-pulse"></div>
          <div className="absolute top-3/4 right-1/4 w-1 h-1 bg-brand-yellow rounded-full opacity-70 animate-ping"></div>
          <div className="absolute top-1/2 left-3/4 w-3 h-3 bg-blue-500 rounded-full opacity-40 animate-bounce"></div>
        </div>
        
        <div className="relative z-10 flex flex-col items-center">
          <div ref={imageRef} className="mb-8">
            <Image 
              src="/placeholder-headshot.svg"
              alt="Video Editor Headshot"
              width={128}
              height={128}
              className="rounded-full border-4 border-blue-500 shadow-2xl hover:border-brand-yellow transition-colors duration-300"
            />
          </div>
          
          <h1 ref={titleRef} className="text-5xl md:text-7xl font-bold mb-4 bg-gradient-to-r from-white via-blue-500 to-brand-purple bg-clip-text text-transparent">
            <span className="inline-block">Masterful</span>{' '}
            <span className="inline-block">Video</span>{' '}
            <span className="inline-block text-blue-500">Editing</span>
          </h1>
          
          <h2 ref={subtitleRef} className="text-5xl md:text-7xl font-bold mb-6 overflow-hidden whitespace-nowrap">
            for Impact
          </h2>
          
          <p ref={descriptionRef} className="text-lg md:text-xl text-gray-300 mb-10 max-w-xl leading-relaxed">
            I transform raw footage into polished, compelling narratives that captivate your audience and elevate your brand. Your vision, expertly edited.
          </p>
          
          <div ref={buttonsRef} className="flex flex-col sm:flex-row items-center space-y-4 sm:space-y-0 sm:space-x-6 mb-12">
            <Link href="/portfolio" className="group bg-gradient-to-r from-blue-500 to-brand-purple hover:from-blue-600 hover:to-brand-purple text-white font-semibold py-3 px-8 rounded-md text-lg flex items-center justify-center w-full sm:w-auto transform hover:scale-105 transition-all duration-300 shadow-lg hover:shadow-xl">
              <FaPlayCircle className="mr-2 group-hover:animate-spin" /> View My Work
            </Link>
            <Link href="/contact" className="group border-2 border-blue-500 hover:bg-gradient-to-r hover:from-blue-500 hover:to-brand-purple hover:border-transparent text-white font-semibold py-3 px-8 rounded-md text-lg flex items-center justify-center w-full sm:w-auto transform hover:scale-105 transition-all duration-300">
              <FaEnvelope className="mr-2 group-hover:animate-pulse" /> Get In Touch
            </Link>
          </div>
          
          <div ref={chevronRef}>
            <FaChevronDown className="text-3xl text-gray-400 cursor-pointer hover:text-brand-yellow transition-colors duration-300" />
          </div>
        </div>
      </section>

      {/* Enhanced Content Section */}
      <section className="py-20 bg-gradient-to-b from-gray-950 to-black text-center relative">
        <div className="container mx-auto px-6">
          <div ref={contentRef}>
            <h2 className="text-4xl md:text-6xl font-bold mb-8 bg-gradient-to-r from-brand-yellow to-brand-purple bg-clip-text text-transparent">
              Why Choose Studio Noir?
            </h2>
            <p className="text-lg md:text-xl text-gray-400 mb-16 max-w-3xl mx-auto leading-relaxed">
              Experience the difference of professional video editing that brings your vision to life with cinematic quality and creative excellence.
            </p>
            
            {/* Stats Section */}
            <div ref={statsRef} className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
              <div className="bg-gradient-to-br from-gray-800 to-gray-900 p-8 rounded-xl shadow-2xl hover:shadow-brand-purple/20 transition-all duration-300 transform hover:-translate-y-2">
                <FaVideo className="text-4xl text-brand-purple mb-4 mx-auto" />
                <h3 className="text-3xl font-bold text-white mb-2">500+</h3>
                <p className="text-gray-400">Projects Completed</p>
              </div>
              <div className="bg-gradient-to-br from-gray-800 to-gray-900 p-8 rounded-xl shadow-2xl hover:shadow-brand-yellow/20 transition-all duration-300 transform hover:-translate-y-2">
                <FaStar className="text-4xl text-brand-yellow mb-4 mx-auto" />
                <h3 className="text-3xl font-bold text-white mb-2">5.0</h3>
                <p className="text-gray-400">Average Rating</p>
              </div>
              <div className="bg-gradient-to-br from-gray-800 to-gray-900 p-8 rounded-xl shadow-2xl hover:shadow-blue-500/20 transition-all duration-300 transform hover:-translate-y-2">
                <FaAward className="text-4xl text-blue-500 mb-4 mx-auto" />
                <h3 className="text-3xl font-bold text-white mb-2">50+</h3>
                <p className="text-gray-400">Awards Won</p>
              </div>
            </div>
          </div>
        </div>
        
        {/* Floating background elements */}
        <div className="absolute top-10 left-10 w-20 h-20 bg-brand-purple/10 rounded-full blur-xl animate-pulse"></div>
        <div className="absolute bottom-10 right-10 w-32 h-32 bg-brand-yellow/10 rounded-full blur-xl animate-pulse"></div>
      </section>
    </div>
  );
}
