import type { Metada<PERSON> } from "next";
import { <PERSON>eist, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";
import "./globals.css";
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import ClientLayout from "./ClientLayout";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "Studio Noir - Professional Video Editing",
  description: "Professional video editing services with creative excellence. Transform your vision into stunning visual stories.",
};

export default function RootLayout({ children }: { children: React.ReactNode }) {
  return (
    <html lang="en">
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased bg-neutral-950 text-neutral-50 overflow-x-hidden`}
      >
        <ClientLayout>
          <Header />
          <main className="min-h-screen relative">
            {children}
          </main>
          <Footer />
        </ClientLayout>
      </body>
    </html>
  );
}
