"use client";

import React from 'react';
import { FaCut, FaVideo, FaMagic } from 'react-icons/fa'; // Example icons

const servicesData = [
  {
    icon: <FaCut className="text-4xl text-blue-400 mb-4" />,
    title: "Basic Video Editing",
    description: "Professional cuts, transitions, and basic color correction for your raw footage. Ideal for vlogs, simple presentations, and social media clips.",
    details: [
      "Clean Cuts & Transitions",
      "Basic Color Correction",
      "Audio Syncing & Leveling",
      "Up to 1080p HD Output"
    ]
  },
  {
    icon: <FaVideo className="text-4xl text-blue-400 mb-4" />,
    title: "Advanced Post-Production",
    description: "Comprehensive editing including advanced color grading, motion graphics, sound design, and special effects to bring your creative vision to life.",
    details: [
      "Advanced Color Grading & LUTs",
      "Custom Motion Graphics & Titles",
      "Sound Design & Mixing",
      "Visual Effects (VFX) Basics",
      "4K/UHD Output Options"
    ]
  },
  {
    icon: <FaMagic className="text-4xl text-blue-400 mb-4" />,
    title: "Specialized Editing Services",
    description: "Tailored editing solutions for specific needs such as promotional videos, documentaries, short films, music videos, and corporate content.",
    details: [
      "Promotional & Brand Videos",
      "Documentary & Storytelling Edits",
      "Music Video Production",
      "Corporate & Training Videos",
      "Social Media Content Packages"
    ]
  }
];

export default function ServicesPage() {
  return (
    <div className="bg-slate-900 text-slate-200 min-h-screen py-12 md:py-20">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <header className="text-center mb-12 md:mb-16">
          <h1 className="text-4xl sm:text-5xl font-bold text-white leading-tight">
            Our Video Editing Services
          </h1>
          <p className="mt-4 text-lg sm:text-xl text-slate-300 max-w-2xl mx-auto">
            Crafting compelling narratives and visually stunning videos tailored to your unique needs. Explore our range of professional editing services.
          </p>
        </header>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 md:gap-10">
          {servicesData.map((service, index) => (
            <div 
              key={index} 
              className="bg-slate-800 p-6 md:p-8 rounded-xl shadow-2xl flex flex-col items-center text-center hover:shadow-blue-500/30 transition-shadow duration-300"
            >
              {service.icon}
              <h2 className="text-2xl font-semibold text-white mb-3">{service.title}</h2>
              <p className="text-slate-300 mb-6 text-sm leading-relaxed flex-grow">
                {service.description}
              </p>
              <ul className="text-slate-400 text-xs space-y-1 list-disc list-inside mb-6 text-left">
                {service.details.map((detail, i) => (
                  <li key={i}>{detail}</li>
                ))}
              </ul>
              <button className="mt-auto bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-6 rounded-md transition-colors duration-150 text-sm">
                Learn More
              </button>
            </div>
          ))}
        </div>

        <section className="text-center mt-16 md:mt-20 py-10 bg-slate-800/50 rounded-lg px-6">
          <h2 className="text-3xl font-semibold text-white mb-4">Ready to Start Your Project?</h2>
          <p className="text-slate-300 max-w-xl mx-auto mb-8">
            Have a specific project in mind or need a custom quote? We're here to help you transform your footage into something extraordinary.
          </p>
          <a 
            href="/contact" 
            className="bg-green-500 hover:bg-green-600 text-white font-semibold py-3 px-8 rounded-lg transition-colors duration-150 text-lg"
          >
            Get a Free Quote
          </a>
        </section>

      </div>
    </div>
  );
}