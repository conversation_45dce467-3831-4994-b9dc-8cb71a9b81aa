"use client";

import React from 'react';
import { FaCut, FaVideo, FaMagic } from 'react-icons/fa'; // Example icons

const servicesData = [
  {
    icon: <FaCut className="text-5xl text-brand-primary mb-4" />,
    title: "Basic Video Editing",
    description: "Professional cuts, transitions, and basic color correction for your raw footage. Ideal for vlogs, simple presentations, and social media clips.",
    details: [
      "Clean Cuts & Transitions",
      "Basic Color Correction",
      "Audio Syncing & Leveling",
      "Up to 1080p HD Output"
    ]
  },
  {
    icon: <FaVideo className="text-5xl text-brand-secondary mb-4" />,
    title: "Advanced Post-Production",
    description: "Comprehensive editing including advanced color grading, motion graphics, sound design, and special effects to bring your creative vision to life.",
    details: [
      "Advanced Color Grading & LUTs",
      "Custom Motion Graphics & Titles",
      "Sound Design & Mixing",
      "Visual Effects (VFX) Basics",
      "4K/UHD Output Options"
    ]
  },
  {
    icon: <FaMagic className="text-5xl text-brand-accent mb-4" />,
    title: "Specialized Editing Services",
    description: "Tailored editing solutions for specific needs such as promotional videos, documentaries, short films, music videos, and corporate content.",
    details: [
      "Promotional & Brand Videos",
      "Documentary & Storytelling Edits",
      "Music Video Production",
      "Corporate & Training Videos",
      "Social Media Content Packages"
    ]
  }
];

export default function ServicesPage() {
  return (
    <div className="bg-gradient-dark text-neutral-200 min-h-screen py-16 md:py-24">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <header className="text-center mb-16 md:mb-20">
          <h1 className="heading-xl text-gradient-brand mb-6">
            Our Video Editing Services
          </h1>
          <p className="text-xl md:text-2xl text-neutral-300 max-w-3xl mx-auto leading-relaxed font-light">
            Crafting compelling narratives and visually stunning videos tailored to your unique needs. Explore our range of professional editing services.
          </p>
        </header>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 md:gap-10">
          {servicesData.map((service, index) => (
            <div
              key={index}
              className="card-interactive p-8 group text-center"
            >
              <div className="mb-6 group-hover:scale-110 transition-transform duration-300">
                {service.icon}
              </div>
              <h2 className="text-2xl font-semibold text-white mb-4">{service.title}</h2>
              <p className="text-neutral-300 mb-6 leading-relaxed flex-grow">
                {service.description}
              </p>
              <ul className="text-neutral-400 text-sm space-y-2 mb-8 text-left">
                {service.details.map((detail, i) => (
                  <li key={i} className="flex items-center">
                    <span className="w-2 h-2 bg-brand-primary rounded-full mr-3 flex-shrink-0"></span>
                    {detail}
                  </li>
                ))}
              </ul>
              <button className="btn-primary w-full">
                Learn More
              </button>
            </div>
          ))}
        </div>

        <section className="text-center mt-20 md:mt-24 py-16 glass-card">
          <h2 className="heading-md text-gradient-primary mb-6">Ready to Start Your Project?</h2>
          <p className="text-neutral-300 max-w-2xl mx-auto mb-10 text-lg leading-relaxed">
            Have a specific project in mind or need a custom quote? We're here to help you transform your footage into something extraordinary.
          </p>
          <a
            href="/contact"
            className="btn-secondary text-lg px-10 py-4"
          >
            Get a Free Quote
          </a>
        </section>

      </div>
    </div>
  );
}