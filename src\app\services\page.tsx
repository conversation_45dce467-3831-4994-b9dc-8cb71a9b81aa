"use client";

import React from 'react';
import { FaCut, FaVideo, FaMagic } from 'react-icons/fa'; // Example icons

const servicesData = [
  {
    icon: <FaCut className="text-5xl text-purple-400 mb-4" />,
    title: "Basic Video Editing",
    description: "Professional cuts, transitions, and basic color correction for your raw footage. Ideal for vlogs, simple presentations, and social media clips.",
    details: [
      "Clean Cuts & Transitions",
      "Basic Color Correction",
      "Audio Syncing & Leveling",
      "Up to 1080p HD Output"
    ]
  },
  {
    icon: <FaVideo className="text-5xl text-blue-400 mb-4" />,
    title: "Advanced Post-Production",
    description: "Comprehensive editing including advanced color grading, motion graphics, sound design, and special effects to bring your creative vision to life.",
    details: [
      "Advanced Color Grading & LUTs",
      "Custom Motion Graphics & Titles",
      "Sound Design & Mixing",
      "Visual Effects (VFX) Basics",
      "4K/UHD Output Options"
    ]
  },
  {
    icon: <FaMagic className="text-5xl text-yellow-400 mb-4" />,
    title: "Specialized Editing Services",
    description: "Tailored editing solutions for specific needs such as promotional videos, documentaries, short films, music videos, and corporate content.",
    details: [
      "Promotional & Brand Videos",
      "Documentary & Storytelling Edits",
      "Music Video Production",
      "Corporate & Training Videos",
      "Social Media Content Packages"
    ]
  }
];

export default function ServicesPage() {
  return (
    <div className="bg-gradient-to-br from-gray-900 via-purple-900 to-blue-900 text-white min-h-screen py-12 md:py-20">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <header className="text-center mb-12 md:mb-16">
          <h1 className="text-4xl sm:text-5xl font-bold bg-gradient-to-r from-white via-purple-300 to-blue-300 bg-clip-text text-transparent leading-tight mb-6">
            Our Video Editing Services
          </h1>
          <p className="mt-4 text-lg sm:text-xl text-gray-200 max-w-2xl mx-auto">
            Crafting compelling narratives and visually stunning videos tailored to your unique needs. Explore our range of professional editing services.
          </p>
        </header>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 md:gap-8">
          {servicesData.map((service, index) => (
            <div
              key={index}
              className="bg-gradient-to-br from-gray-800/50 to-gray-900/50 backdrop-blur-sm border border-purple-500/30 rounded-xl p-8 shadow-xl hover:shadow-2xl hover:shadow-purple-500/20 hover:-translate-y-2 transition-all duration-300 flex flex-col group"
            >
              <div className="mb-6 group-hover:scale-110 transition-transform duration-300">
                {service.icon}
              </div>
              <h3 className="text-2xl font-semibold text-white mb-4">{service.title}</h3>
              <p className="text-gray-300 mb-6 flex-grow leading-relaxed">{service.description}</p>
              <ul className="text-sm text-gray-400 mb-8 space-y-2">
                {service.details.map((detail, detailIndex) => (
                  <li key={detailIndex} className="flex items-center">
                    <span className="w-2 h-2 bg-purple-400 rounded-full mr-3 flex-shrink-0"></span>
                    {detail}
                  </li>
                ))}
              </ul>
              <button className="mt-auto bg-gradient-to-r from-purple-600 to-blue-600 hover:from-blue-600 hover:to-purple-600 text-white font-semibold py-3 px-6 rounded-lg transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-105">
                Learn More
              </button>
            </div>
          ))}
        </div>

        <section className="text-center mt-16 md:mt-20 py-12 bg-gradient-to-r from-purple-800/30 to-blue-800/30 backdrop-blur-sm border border-purple-500/30 rounded-xl px-8">
          <h2 className="text-3xl font-semibold bg-gradient-to-r from-white to-purple-300 bg-clip-text text-transparent mb-6">Ready to Start Your Project?</h2>
          <p className="text-gray-300 max-w-2xl mx-auto mb-8 text-lg leading-relaxed">
            Have a specific project in mind or need a custom quote? We're here to help you transform your footage into something extraordinary.
          </p>
          <a
            href="/contact"
            className="bg-gradient-to-r from-yellow-500 to-orange-500 hover:from-orange-500 hover:to-yellow-500 text-white font-semibold py-4 px-10 rounded-lg transition-all duration-300 text-lg shadow-lg hover:shadow-xl transform hover:scale-105"
          >
            Get a Free Quote
          </a>
        </section>

      </div>
    </div>
  );
}