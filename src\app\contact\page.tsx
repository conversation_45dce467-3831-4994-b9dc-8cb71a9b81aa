"use client";

import React, { useState } from 'react';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, FaTwitter, FaFacebookF, FaChevronLeft, FaChevronRight } from 'react-icons/fa';

const videoServices = [
  "Select editing specialization...",
  "Promotional Videos",
  "Corporate Videos",
  "Social Media Content",
  "Wedding Highlights",
  "Music Videos",
  "Short Films",
  "Documentaries",
  "Tutorials & E-learning",
  "Other (Specify in details)"
];

export default function ContactPage() {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    service: videoServices[0],
    details: '',
    budget: '',
    timeline: ''
  });

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prevState => ({
      ...prevState,
      [name]: value
    }));
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    // Handle form submission logic (e.g., send data to an API)
    console.log('Form data submitted:', formData);
    alert('Inquiry sent! (This is a demo)');
  };

  // Placeholder for calendar logic
  const currentMonth = "July 2024";
  const daysInMonth = Array.from({ length: 31 }, (_, i) => i + 1);
  const today = 5; // Example: 5th is highlighted

  return (
    <div className="bg-slate-900 text-white min-h-screen py-12 sm:py-16 px-4 sm:px-6 lg:px-8">
      <div className="max-w-5xl mx-auto">
        <header className="text-center mb-12 md:mb-16">
          <h1 className="text-4xl sm:text-5xl font-bold mb-3">
            Elevate Your Vision with <span className="text-blue-500">Expert Video Editing.</span>
          </h1>
          <p className="text-lg sm:text-xl text-slate-300 max-w-3xl mx-auto">
            Ready to discuss your next video editing project? Fill out the form below or reach out directly.
          </p>
        </header>

        {/* Main Content Area: Form + Contact/Booking Sections */} 
        <div className="bg-slate-800 p-6 sm:p-8 md:p-10 rounded-xl shadow-2xl mb-12 md:mb-16">
          <form onSubmit={handleSubmit} className="grid grid-cols-1 md:grid-cols-2 gap-x-8 gap-y-6">
            {/* Column 1: Name, Email, Service */}
            <div className="space-y-6">
              <div>
                <label htmlFor="name" className="block text-sm font-medium text-slate-300 mb-1">Your Name</label>
                <input 
                  type="text" 
                  name="name" 
                  id="name" 
                  value={formData.name} 
                  onChange={handleChange} 
                  placeholder="e.g., Jane Doe" 
                  className="mt-1 block w-full px-4 py-3 bg-slate-700 border border-slate-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 sm:text-sm text-white placeholder-slate-400" 
                  required 
                />
              </div>
              <div>
                <label htmlFor="email" className="block text-sm font-medium text-slate-300 mb-1">Your Email</label>
                <input 
                  type="email" 
                  name="email" 
                  id="email" 
                  value={formData.email} 
                  onChange={handleChange} 
                  placeholder="e.g., <EMAIL>" 
                  className="mt-1 block w-full px-4 py-3 bg-slate-700 border border-slate-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 sm:text-sm text-white placeholder-slate-400" 
                  required 
                />
              </div>
              <div>
                <label htmlFor="service" className="block text-sm font-medium text-slate-300 mb-1">Video Editing Service</label>
                <select 
                  name="service" 
                  id="service" 
                  value={formData.service} 
                  onChange={handleChange} 
                  className="mt-1 block w-full px-4 py-3 bg-slate-700 border border-slate-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 sm:text-sm text-white"
                >
                  {videoServices.map(service => (
                    <option key={service} value={service} disabled={service === videoServices[0] && formData.service !== videoServices[0]}>{service}</option>
                  ))}
                </select>
              </div>
            </div>

            {/* Column 2: Project Details */}
            <div>
              <label htmlFor="details" className="block text-sm font-medium text-slate-300 mb-1">Project Details</label>
              <textarea 
                id="details" 
                name="details" 
                rows={8} 
                value={formData.details} 
                onChange={handleChange} 
                placeholder="Describe your video editing needs, vision, and any specific requirements..." 
                className="mt-1 block w-full px-4 py-3 bg-slate-700 border border-slate-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 sm:text-sm text-white placeholder-slate-400 resize-none"
                required
              ></textarea>
            </div>

            {/* Spanning full width: Budget, Timeline, Submit */}
            <div className="md:col-span-2 grid grid-cols-1 sm:grid-cols-2 gap-x-8 gap-y-6">
              <div>
                <label htmlFor="budget" className="block text-sm font-medium text-slate-300 mb-1">Budget (Optional)</label>
                <input 
                  type="text" 
                  name="budget" 
                  id="budget" 
                  value={formData.budget} 
                  onChange={handleChange} 
                  placeholder="e.g., $5,000 - $10,000" 
                  className="mt-1 block w-full px-4 py-3 bg-slate-700 border border-slate-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 sm:text-sm text-white placeholder-slate-400" 
                />
              </div>
              <div>
                <label htmlFor="timeline" className="block text-sm font-medium text-slate-300 mb-1">Timeline (Optional)</label>
                <input 
                  type="text" 
                  name="timeline" 
                  id="timeline" 
                  value={formData.timeline} 
                  onChange={handleChange} 
                  placeholder="e.g., 4-6 weeks" 
                  className="mt-1 block w-full px-4 py-3 bg-slate-700 border border-slate-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 sm:text-sm text-white placeholder-slate-400" 
                />
              </div>
            </div>
            
            <div className="md:col-span-2">
              <button 
                type="submit" 
                className="w-full flex justify-center py-3 px-6 border border-transparent rounded-md shadow-sm text-base font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-slate-800 focus:ring-blue-500 transition-colors duration-150"
              >
                Send Video Editing Inquiry
              </button>
            </div>
          </form>
        </div>

        {/* Bottom Section: Direct Contact & Booking */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 md:gap-12">
          {/* Direct Contact */}
          <div className="bg-slate-800 p-6 sm:p-8 rounded-xl shadow-xl">
            <h2 className="text-2xl font-semibold mb-6 text-slate-100">Direct Contact for Video Editing</h2>
            <div className="space-y-3 text-slate-300">
              <p><strong className="font-medium text-slate-200">Email:</strong> <a href="mailto:<EMAIL>" className="text-blue-400 hover:text-blue-300"><EMAIL></a></p>
              <p><strong className="font-medium text-slate-200">Phone:</strong> <a href="tel:+***********" className="text-blue-400 hover:text-blue-300">(*************</a></p>
              <p><strong className="font-medium text-slate-200">Response Time:</strong> Typically within 24 business hours for video editing inquiries.</p>
              <p><strong className="font-medium text-slate-200">Location:</strong> Los Angeles, CA (PST)</p>
            </div>
            <div className="mt-8">
              <h3 className="text-lg font-semibold mb-3 text-slate-200">Follow Our Work</h3>
              <div className="flex space-x-4">
                <a href="#" className="text-slate-400 hover:text-blue-400 transition-colors"><FaInstagram size={24} /></a>
                <a href="#" className="text-slate-400 hover:text-blue-400 transition-colors"><FaTwitter size={24} /></a>
                <a href="#" className="text-slate-400 hover:text-blue-400 transition-colors"><FaFacebookF size={24} /></a>
              </div>
            </div>
          </div>

          {/* Book Consultation */}
          <div className="bg-slate-800 p-6 sm:p-8 rounded-xl shadow-xl">
            <h2 className="text-2xl font-semibold mb-6 text-slate-100">Book a Video Editing Consultation</h2>
            <p className="text-slate-300 mb-6">Schedule a 30-minute call to discuss your video editing project in detail.</p>
            
            {/* Placeholder Calendar */}
            <div className="bg-slate-700 p-4 rounded-md mb-6">
              <div className="flex items-center justify-between mb-3">
                <button className="p-1 text-slate-400 hover:text-white"><FaChevronLeft size={20} /></button>
                <h4 className="font-semibold text-slate-200">{currentMonth}</h4>
                <button className="p-1 text-slate-400 hover:text-white"><FaChevronRight size={20} /></button>
              </div>
              <div className="grid grid-cols-7 gap-1 text-center text-sm text-slate-400 mb-2">
                <span>S</span><span>M</span><span>T</span><span>W</span><span>T</span><span>F</span><span>S</span>
              </div>
              <div className="grid grid-cols-7 gap-1 text-center">
                {/* Placeholder for empty days at start of month */}
                {Array.from({length: 4}).map((_, i) => <div key={`empty-${i}`} className="p-1"></div>) }
                {daysInMonth.map(day => (
                  <button 
                    key={day} 
                    className={`p-2 rounded-full w-8 h-8 flex items-center justify-center mx-auto 
                      ${day === today ? 'bg-blue-500 text-white font-semibold' : 'hover:bg-slate-600 text-slate-200'}
                      ${day < today ? 'text-slate-500 cursor-not-allowed' : ''}` // Example: disable past dates
                    }
                    disabled={day < today}
                  >
                    {day}
                  </button>
                ))}
              </div>
            </div>

            <button className="w-full flex justify-center py-3 px-6 border border-transparent rounded-md shadow-sm text-base font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-slate-800 focus:ring-blue-500 transition-colors duration-150">
              Book Consultation
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}