'use client';

import Link from 'next/link';
import { FaFacebook, FaTwitter, FaInstagram, FaLinkedin } from 'react-icons/fa';
import { useEffect, useRef } from 'react';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';

// Register ScrollTrigger plugin
if (typeof window !== 'undefined') {
  gsap.registerPlugin(ScrollTrigger);
}

const Footer = () => {
  const footerRef = useRef<HTMLElement>(null);
  const socialIconsRef = useRef<HTMLDivElement>(null);
  const copyrightRef = useRef<HTMLParagraphElement>(null);
  const linksRef = useRef<HTMLDivElement>(null);

  const socialIcons = [
    { Icon: FaFacebook, href: "#", color: "#1877F2", name: "Facebook" },
    { Icon: FaTwitter, href: "#", color: "#1DA1F2", name: "Twitter" },
    { Icon: FaInstagram, href: "#", color: "#E4405F", name: "Instagram" },
    { Icon: FaLinkedin, href: "#", color: "#0A66C2", name: "LinkedIn" }
  ];

  useEffect(() => {
    const ctx = gsap.context(() => {
      // Footer entrance animation with ScrollTrigger
      const tl = gsap.timeline({
        scrollTrigger: {
          trigger: footerRef.current,
          start: "top 90%",
          end: "bottom 100%",
          toggleActions: "play none none reverse"
        }
      });

      // Footer slide up
      tl.fromTo(footerRef.current, 
        { y: 50, opacity: 0 },
        { y: 0, opacity: 1, duration: 0.8, ease: "power3.out" }
      )
      // Social icons stagger animation
      .fromTo(socialIconsRef.current?.children || [], 
        { y: 30, opacity: 0, scale: 0 },
        { 
          y: 0, 
          opacity: 1, 
          scale: 1, 
          duration: 0.5, 
          stagger: 0.1, 
          ease: "back.out(1.7)" 
        },
        "-=0.4"
      )
      // Copyright text
      .fromTo(copyrightRef.current, 
        { y: 20, opacity: 0 },
        { y: 0, opacity: 1, duration: 0.6, ease: "power2.out" },
        "-=0.3"
      )
      // Footer links
      .fromTo(linksRef.current?.children || [], 
        { y: 20, opacity: 0 },
        { y: 0, opacity: 1, duration: 0.4, stagger: 0.1, ease: "power2.out" },
        "-=0.2"
      );

      // Add hover animations for social icons
      const socialIconElements = socialIconsRef.current?.children;
      if (socialIconElements) {
        Array.from(socialIconElements).forEach((icon, index) => {
          const iconElement = icon as HTMLElement;
          const socialData = socialIcons[index];
          
          const handleMouseEnter = () => {
            gsap.to(iconElement, {
              scale: 1.3,
              rotation: 10,
              color: socialData.color,
              duration: 0.3,
              ease: "back.out(1.7)"
            });
          };

          const handleMouseLeave = () => {
            gsap.to(iconElement, {
              scale: 1,
              rotation: 0,
              color: "#9CA3AF", // gray-400
              duration: 0.3,
              ease: "power2.out"
            });
          };

          iconElement.addEventListener('mouseenter', handleMouseEnter);
          iconElement.addEventListener('mouseleave', handleMouseLeave);

          // Cleanup function
          return () => {
            iconElement.removeEventListener('mouseenter', handleMouseEnter);
            iconElement.removeEventListener('mouseleave', handleMouseLeave);
          };
        });
      }

      // Add hover animations for footer links
      const linkElements = linksRef.current?.children;
      if (linkElements) {
        Array.from(linkElements).forEach((link) => {
          const linkElement = link as HTMLElement;
          
          const handleMouseEnter = () => {
            gsap.to(linkElement, {
              y: -2,
              color: "#FFD700", // brand-yellow
              duration: 0.3,
              ease: "power2.out"
            });
          };

          const handleMouseLeave = () => {
            gsap.to(linkElement, {
              y: 0,
              color: "#9CA3AF", // gray-400
              duration: 0.3,
              ease: "power2.out"
            });
          };

          linkElement.addEventListener('mouseenter', handleMouseEnter);
          linkElement.addEventListener('mouseleave', handleMouseLeave);
        });
      }

      // Floating animation for the entire footer
      gsap.to(footerRef.current, {
        y: "+=3",
        duration: 3,
        ease: "sine.inOut",
        yoyo: true,
        repeat: -1
      });

    }, footerRef);

    return () => ctx.revert();
  }, []);

  return (
    <footer 
      ref={footerRef}
      className="bg-gradient-to-t from-black via-purple-900 to-gray-900 text-white py-16 mt-16 border-t border-purple-500/30 relative overflow-hidden"
    >
      {/* Animated background elements */}
      <div className="absolute inset-0 opacity-20">
        <div className="absolute top-8 left-8 w-3 h-3 bg-yellow-400 rounded-full animate-pulse"></div>
        <div className="absolute top-12 right-12 w-2 h-2 bg-purple-400 rounded-full animate-ping"></div>
        <div className="absolute bottom-10 left-1/4 w-2.5 h-2.5 bg-blue-400 rounded-full animate-bounce"></div>
        <div className="absolute bottom-8 right-1/3 w-2 h-2 bg-yellow-400 rounded-full animate-pulse"></div>
        <div className="absolute top-1/2 left-1/2 w-32 h-32 bg-purple-500/10 rounded-full blur-3xl"></div>
      </div>
      
      <div className="container mx-auto px-6 text-center relative z-10">
        {/* Enhanced Social Media Icons */}
        <div ref={socialIconsRef} className="flex justify-center space-x-10 mb-8">
          {socialIcons.map(({ Icon, href, name }, index) => (
            <SocialIcon key={name} Icon={Icon} href={href} name={name} />
          ))}
        </div>

        {/* Enhanced Copyright */}
        <p
          ref={copyrightRef}
          className="text-gray-300 text-base mb-6 font-medium"
        >
          &copy; {new Date().getFullYear()} Studio Noir. All rights reserved.
        </p>

        {/* Enhanced Footer Links */}
        <div ref={linksRef} className="flex justify-center space-x-8 text-sm">
          <FooterLink href="/privacy-policy">Privacy Policy</FooterLink>
          <FooterLink href="/terms-of-service">Terms of Service</FooterLink>
          <FooterLink href="/contact">Contact Us</FooterLink>
        </div>

        {/* Enhanced Decorative line */}
        <div className="mt-10 w-32 h-0.5 bg-gradient-to-r from-transparent via-yellow-400 to-transparent mx-auto"></div>
      </div>
    </footer>
  );
};

// Individual social icon component
const SocialIcon = ({ Icon, href, name }: {
  Icon: React.ComponentType<{ size: number }>;
  href: string;
  name: string;
}) => {
  return (
    <a
      href={href}
      className="text-gray-400 hover:text-yellow-400 transition-all duration-300 transform hover:scale-110 cursor-pointer p-3 rounded-full hover:bg-purple-500/20 backdrop-blur-sm border border-transparent hover:border-purple-500/30"
      aria-label={name}
    >
      <Icon size={32} />
    </a>
  );
};

// Individual footer link component
const FooterLink = ({ href, children }: { href: string; children: React.ReactNode }) => {
  return (
    <Link
      href={href}
      className="text-gray-400 hover:text-yellow-400 transition-all duration-300 transform hover:scale-105 cursor-pointer relative font-medium"
    >
      {children}
    </Link>
  );
};

export default Footer;