'use client';

import React from 'react';
import { cn } from '@/lib/utils';

export interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  label?: string;
  error?: string;
  helperText?: string;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  variant?: 'default' | 'filled' | 'outline';
  inputSize?: 'sm' | 'md' | 'lg';
}

const Input = React.forwardRef<HTMLInputElement, InputProps>(
  ({ 
    className, 
    type = 'text',
    label,
    error,
    helperText,
    leftIcon,
    rightIcon,
    variant = 'default',
    inputSize = 'md',
    id,
    ...props 
  }, ref) => {
    const inputId = id || `input-${Math.random().toString(36).substr(2, 9)}`;
    
    const baseStyles = "w-full transition-all duration-200 focus-ring disabled:opacity-50 disabled:cursor-not-allowed";
    
    const variants = {
      default: "bg-neutral-800/50 border border-neutral-700 text-white placeholder-neutral-400 focus:border-brand-primary focus:bg-neutral-800/70",
      filled: "bg-neutral-800 border-0 text-white placeholder-neutral-400 focus:bg-neutral-700",
      outline: "bg-transparent border-2 border-neutral-600 text-white placeholder-neutral-400 focus:border-brand-primary"
    };

    const sizes = {
      sm: "px-3 py-2 text-sm rounded-md",
      md: "px-4 py-3 text-base rounded-lg",
      lg: "px-5 py-4 text-lg rounded-lg"
    };

    const iconSizes = {
      sm: "w-4 h-4",
      md: "w-5 h-5",
      lg: "w-6 h-6"
    };

    return (
      <div className="w-full">
        {label && (
          <label 
            htmlFor={inputId}
            className="block text-sm font-medium text-neutral-200 mb-2"
          >
            {label}
          </label>
        )}
        
        <div className="relative">
          {leftIcon && (
            <div className={cn(
              "absolute left-3 top-1/2 transform -translate-y-1/2 text-neutral-400",
              iconSizes[inputSize]
            )}>
              {leftIcon}
            </div>
          )}
          
          <input
            type={type}
            id={inputId}
            className={cn(
              baseStyles,
              variants[variant],
              sizes[inputSize],
              leftIcon && "pl-10",
              rightIcon && "pr-10",
              error && "border-error-500 focus:border-error-500",
              className
            )}
            ref={ref}
            {...props}
          />
          
          {rightIcon && (
            <div className={cn(
              "absolute right-3 top-1/2 transform -translate-y-1/2 text-neutral-400",
              iconSizes[inputSize]
            )}>
              {rightIcon}
            </div>
          )}
        </div>
        
        {error && (
          <p className="mt-1 text-sm text-error-500">
            {error}
          </p>
        )}
        
        {helperText && !error && (
          <p className="mt-1 text-sm text-neutral-400">
            {helperText}
          </p>
        )}
      </div>
    );
  }
);

Input.displayName = "Input";

export default Input;
