"use client";

import React from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { FaPlay, FaArrowLeft } from 'react-icons/fa'; // Assuming FaArrowLeft for back button

// Placeholder data - in a real app, this would come from a CMS or API based on projectId
const projectData = {
  id: 1,
  title: "Project Title Example",
  client: "Client Name Here",
  description: "A detailed description of the project, highlighting the challenges, solutions, and overall impact. This project involved advanced color grading and motion graphics to achieve a cinematic feel.",
  scope: [
    "Advanced Color Grading",
    "Motion Graphics",
    "Sound Design & Mixing",
    "4K Delivery"
  ],
  mainImage: "/portfolio-thumb-1.svg", // Placeholder, use actual image path
  behindTheEditImages: [
    "/portfolio-thumb-2.svg", // Placeholder
    "/portfolio-thumb-3.svg", // Placeholder
    "/portfolio-thumb-4.svg"  // Placeholder
  ],
  editingPhilosophy: "Our editing philosophy centers on deeply connecting with the audience. By meticulously crafting each scene, we aim to create a final product that is not only visually stunning but also emotionally resonant. For this project, the goal was to immerse viewers in the narrative, using pacing and visual storytelling to deliver a powerful and memorable experience.",
  relatedProjects: [
    { id: 2, title: "Project Alpha", thumbnail: "/portfolio-thumb-5.svg", category: "Commercial" },
    { id: 3, title: "Project Beta", thumbnail: "/portfolio-thumb-6.svg", category: "Short Film" },
    { id: 4, title: "Project Gamma", thumbnail: "/portfolio-thumb-7.svg", category: "Music Video" },
  ]
};

export default function ProjectDetailPage({ params }) {
  // In a real app, you would fetch project data based on params.projectId
  // For this example, we're using the static projectData
  const project = projectData;

  if (!project) {
    return <div className="text-center py-20 text-white">Project not found.</div>;
  }

  return (
    <div className="bg-slate-900 text-slate-200 min-h-screen">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-8 md:py-12">
        {/* Back to Portfolio Link */}
        <div className="mb-8">
          <Link href="/portfolio" className="inline-flex items-center text-blue-400 hover:text-blue-300 transition-colors">
            <FaArrowLeft className="mr-2" />
            Back to Portfolio
          </Link>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 md:gap-12">
          {/* Left Column: Main Content */}
          <div className="lg:col-span-2 space-y-8 md:space-y-10">
            {/* Main Project Image/Video Player */}
            <div className="relative aspect-video bg-slate-800 rounded-lg overflow-hidden shadow-xl">
              <Image 
                src={project.mainImage} 
                alt={`Main visual for ${project.title}`}
                fill
                style={{ objectFit: 'cover' }}
                priority
              />
              {/* Overlay for video play button if it's a video */}
              {/* <div className="absolute inset-0 bg-black bg-opacity-30 flex items-center justify-center">
                <FaPlay className="text-white text-6xl opacity-80 hover:opacity-100 transition-opacity" />
              </div> */}
            </div>

            {/* Editing Philosophy & Impact */}
            <section>
              <h2 className="text-2xl md:text-3xl font-semibold text-white mb-4">Editing Philosophy & Impact</h2>
              <p className="text-slate-300 leading-relaxed">
                {project.editingPhilosophy}
              </p>
            </section>

            {/* Behind the Edit Gallery */}
            <section>
              <h2 className="text-2xl md:text-3xl font-semibold text-white mb-4">Behind the Edit</h2>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 md:gap-6">
                {project.behindTheEditImages.map((src, index) => (
                  <div key={index} className="relative aspect-video bg-slate-800 rounded-md overflow-hidden shadow-lg">
                    <Image 
                      src={src} 
                      alt={`Behind the edit image ${index + 1}`}
                      fill
                      style={{ objectFit: 'cover' }}
                      className="hover:scale-105 transition-transform duration-300"
                    />
                  </div>
                ))}
              </div>
            </section>
          </div>

          {/* Right Column: Project Details Sidebar */}
          <aside className="lg:col-span-1 space-y-6 md:space-y-8">
            <div className="bg-slate-800 p-6 rounded-lg shadow-xl">
              <h1 className="text-3xl md:text-4xl font-bold text-white mb-1">{project.title}</h1>
              <p className="text-blue-400 mb-6 text-sm">Client: {project.client}</p>
              
              <section className="mb-6">
                <h3 className="text-lg font-semibold text-white mb-2">Description</h3>
                <p className="text-slate-300 text-sm leading-relaxed">
                  {project.description}
                </p>
              </section>

              <section className="mb-6">
                <h3 className="text-lg font-semibold text-white mb-2">Key Techniques / Scope</h3>
                <ul className="list-disc list-inside text-slate-300 text-sm space-y-1">
                  {project.scope.map((item, index) => (
                    <li key={index}>{item}</li>
                  ))}
                </ul>
              </section>

              <button className="w-full bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-4 rounded-md transition-colors duration-150">
                Let's Re-create Your Vision
              </button>
            </div>
          </aside>
        </div>

        {/* Related Projects */}
        <section className="mt-12 md:mt-16 pt-8 border-t border-slate-700">
          <h2 className="text-2xl md:text-3xl font-semibold text-white mb-6 text-center">Related Projects</h2>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 md:gap-8">
            {project.relatedProjects.map(related => (
              <Link key={related.id} href={`/portfolio/project-${related.id}`} className="group bg-slate-800 rounded-lg overflow-hidden shadow-xl hover:shadow-2xl transition-all duration-300 block">
                <div className="relative aspect-video">
                  <Image 
                    src={related.thumbnail} 
                    alt={related.title} 
                    fill
                    style={{ objectFit: 'cover' }}
                    className="group-hover:scale-105 transition-transform duration-300"
                  />
                </div>
                <div className="p-4">
                  <h3 className="text-lg font-semibold text-white mb-1 group-hover:text-blue-400 transition-colors">{related.title}</h3>
                  <p className="text-sm text-slate-400">{related.category}</p>
                </div>
              </Link>
            ))}
          </div>
        </section>
      </div>
    </div>
  );
}