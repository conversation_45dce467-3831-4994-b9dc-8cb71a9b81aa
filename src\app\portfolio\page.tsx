'use client';

import React, { useState, useMemo, useEffect, useRef } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';

// Register GSAP plugins
if (typeof window !== 'undefined') {
  gsap.registerPlugin(ScrollTrigger);
}

// Placeholder data - replace with your actual project data
const allProjects = [
  // VISUALS Style Items (can be mixed or distinct)
  { id: 'v1', title: 'Scenic Landscape', category: 'All Projects', imageUrl: '/portfolio-thumb-1.svg', videoUrl: '#', duration: '0:30', type: 'visuals' },
  { id: 'v2', title: 'Corporate Presentation', category: 'Product Commercials', imageUrl: '/portfolio-thumb-2.svg', videoUrl: '#', duration: '1:15', type: 'visuals', isVideoPlaceholder: true },
  { id: 'v3', title: 'Mountain Timelapse', category: 'Other Creative Works', imageUrl: '/portfolio-thumb-3.svg', videoUrl: '#', duration: '0:45', type: 'visuals' },
  { id: 'v4', title: 'Abstract Animation', category: 'AMVs', imageUrl: '/portfolio-thumb-4.svg', videoUrl: '#', duration: '2:00', type: 'visuals', isVideoPlaceholder: true },
  { id: 'v5', title: 'Curtain Reveal', category: 'WIS Edits', imageUrl: '/portfolio-thumb-5.svg', videoUrl: '#', duration: '0:10', type: 'visuals', isVideoPlaceholder: true },
  { id: 'v6', title: 'Website Showcase', category: 'YouTube-specific Edits', imageUrl: '/portfolio-thumb-6.svg', videoUrl: '#', duration: '1:30', type: 'visuals', isVideoPlaceholder: true }, // Placeholder for screen-like content
  { id: 'v7', title: 'Lake View', category: 'Podcast-style Edits', imageUrl: '/portfolio-thumb-7.svg', videoUrl: '#', duration: '0:55', type: 'visuals' },
  { id: 'v8', title: 'Forest Drone Shot', category: 'All Projects', imageUrl: '/portfolio-thumb-8.svg', videoUrl: '#', duration: '1:05', type: 'visuals', isLarge: true },

  // Studio Noir Style Items
  { id: 'sn1', title: 'Luxury Auto - Edited for Impact', category: 'Commercial Video Editing', imageUrl: '/portfolio-thumb-1.svg', videoUrl: '#', duration: '0:30', description: 'Dynamic cuts and sleek visuals for a luxury car brand.', type: 'studionoir' },
  { id: 'sn2', title: 'Fashion Forward - Dynamic Edit', category: 'Commercial Video Editing', imageUrl: '/portfolio-thumb-2.svg', videoUrl: '#', duration: '0:45', description: 'High-energy fashion promo with a modern feel.', type: 'studionoir' },
  { id: 'sn3', title: 'Tech Innovations - Sleek Edit', category: 'Product Commercials', imageUrl: '/portfolio-thumb-3.svg', videoUrl: '#', duration: '1:15', description: 'Clean and informative edit for a new tech product.', type: 'studionoir' },
  { id: 'sn4', title: 'Culinary Delights - Appetizing Edit', category: 'Commercial Video Editing', imageUrl: '/portfolio-thumb-4.svg', videoUrl: '#', duration: '0:55', description: 'Mouth-watering visuals for a food client.', type: 'studionoir' },
  { id: 'sn5', title: 'Wanderlust - Adventure Edit', category: 'Other Creative Works', imageUrl: '/portfolio-thumb-5.svg', videoUrl: '#', duration: '1:08', description: 'Captivating travel montage.', type: 'studionoir' },
  { id: 'sn6', title: 'Active Lifestyle - Energetic Edit', category: 'Commercial Video Editing', imageUrl: '/portfolio-thumb-6.svg', videoUrl: '#', duration: '0:50', description: 'Fast-paced edit showcasing an active lifestyle brand.', type: 'studionoir' },
  { id: 'sn7', title: 'Indie Gem - Narrative Edit', category: 'Short Film Editing', imageUrl: '/portfolio-thumb-7.svg', videoUrl: '#', duration: '13:30', description: 'Story-driven piece for an independent short film.', type: 'studionoir' },
  { id: 'sn8', title: 'Dramatic Tales - Story Edit', category: 'Short Film Editing', imageUrl: '/portfolio-thumb-8.svg', videoUrl: '#', duration: '10:45', description: 'Compelling narrative edit with emotional depth.', type: 'studionoir' },
  { id: 'sn9', title: 'Suspenseful Thrills - Tension Edit', category: 'Short Film Editing', imageUrl: '/portfolio-thumb-1.svg', videoUrl: '#', duration: '10:45', description: 'Building suspense through careful pacing and sound.', type: 'studionoir' },
  { id: 'sn10', title: 'Pop Sensations - Visual Edit', category: 'Music Video Editing', imageUrl: '/portfolio-thumb-2.svg', videoUrl: '#', duration: '3:15', description: 'Eye-catching visuals for a pop music video.', type: 'studionoir' }, 
  { id: 'sn11', title: 'Hip Hop Beats - Rhythmic Edit', category: 'AMVs', imageUrl: '/portfolio-thumb-3.svg', videoUrl: '#', duration: '4:10', description: 'Rhythm-focused edit for a hip hop track.', type: 'studionoir' },
  { id: 'sn12', title: 'Electronic Vibes - Dynamic Edit', category: 'Music Video Editing', imageUrl: '/portfolio-thumb-4.svg', videoUrl: '#', duration: '3:50', description: 'Dynamic and energetic edit for an electronic music video.', type: 'studionoir' },
  { id: 'sn13', title: 'Podcast Highlights - Engaging Edit', category: 'Podcast-style Edits', imageUrl: '/portfolio-thumb-5.svg', videoUrl: '#', duration: '5:00', description: 'Engaging visual representation of podcast audio content.', type: 'studionoir' },
  { id: 'sn14', title: 'Social Snippet - Quick Cut', category: 'YouTube-specific Edits', imageUrl: '/portfolio-thumb-6.svg', videoUrl: '#', duration: '0:58', description: 'Short, impactful edit for social media platforms.', type: 'studionoir' }, 
  { id: 'sn15', title: 'WIS Showreel - Informative Edit', category: 'WIS Edits', imageUrl: '/portfolio-thumb-7.svg', videoUrl: '#', duration: '2:30', description: 'Showcasing key moments from WIS (What I Shot) content.', type: 'studionoir' }, 
];

const filters = [
  'All Projects',
  'AMVs',
  'WIS Edits',
  'Product Commercials',
  'Podcast-style Edits',
  'YouTube-specific Edits',
  'Other Creative Works',
  'Commercial Video Editing',
  'Short Film Editing',
  'Music Video Editing',
];

// VISUALS style portfolio item with GSAP animations
const VisualsPortfolioItem = ({ project, index }: { project: any, index: number }) => {
  const itemRef = useRef<HTMLDivElement>(null);
  const overlayRef = useRef<HTMLDivElement>(null);
  const playIconRef = useRef<SVGSVGElement>(null);

  useEffect(() => {
    const ctx = gsap.context(() => {
      // Initial state
      gsap.set(itemRef.current, { opacity: 0, y: 50, scale: 0.9 });
      
      // Entrance animation with stagger
      gsap.to(itemRef.current, {
        opacity: 1,
        y: 0,
        scale: 1,
        duration: 0.8,
        delay: index * 0.1,
        ease: "power3.out",
        scrollTrigger: {
          trigger: itemRef.current,
          start: "top 90%",
          toggleActions: "play none none reverse"
        }
      });

      // Hover animations
      const handleMouseEnter = () => {
        gsap.to(itemRef.current, {
          scale: 1.05,
          duration: 0.3,
          ease: "power2.out"
        });
        
        if (overlayRef.current) {
          gsap.to(overlayRef.current, {
            opacity: 1,
            duration: 0.3,
            ease: "power2.out"
          });
        }
        
        if (playIconRef.current) {
          gsap.to(playIconRef.current, {
            scale: 1.2,
            rotation: 360,
            duration: 0.5,
            ease: "back.out(1.7)"
          });
        }
      };

      const handleMouseLeave = () => {
        gsap.to(itemRef.current, {
          scale: 1,
          duration: 0.3,
          ease: "power2.out"
        });
        
        if (overlayRef.current) {
          gsap.to(overlayRef.current, {
            opacity: 0,
            duration: 0.3,
            ease: "power2.out"
          });
        }
        
        if (playIconRef.current) {
          gsap.to(playIconRef.current, {
            scale: 1,
            rotation: 0,
            duration: 0.3,
            ease: "power2.out"
          });
        }
      };

      const element = itemRef.current;
      if (element) {
        element.addEventListener('mouseenter', handleMouseEnter);
        element.addEventListener('mouseleave', handleMouseLeave);
        
        return () => {
          element.removeEventListener('mouseenter', handleMouseEnter);
          element.removeEventListener('mouseleave', handleMouseLeave);
        };
      }
    }, itemRef);

    return () => ctx.revert();
  }, [index]);

  return (
    <div 
      ref={itemRef}
      className={`relative rounded-lg overflow-hidden cursor-pointer transform-gpu ${project.isLarge ? 'col-span-2 row-span-2' : ''} ${project.isVideoPlaceholder ? 'bg-gradient-to-br from-gray-700 to-gray-800 flex items-center justify-center' : ''}`}
    >
      {project.isVideoPlaceholder ? (
        <svg className="w-12 h-12 text-gray-400 animate-pulse" fill="currentColor" viewBox="0 0 20 20">
          <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm0-2a6 6 0 100-12 6 6 0 000 12zm-1.09-7.59a.5.5 0 01.09-.7L11.5 5.5a.5.5 0 01.78.41v7.18a.5.5 0 01-.78.41l-2.5-2.21a.5.5 0 01-.09-.7z" clipRule="evenodd" />
        </svg>
      ) : (
        <Image 
          src={project.imageUrl} 
          alt={project.title} 
          width={project.isLarge ? 800 : 400} 
          height={project.isLarge ? 800 : 400} 
          className="w-full h-full object-cover" 
        />
      )}
      {!project.isVideoPlaceholder && (
        <div 
          ref={overlayRef}
          className="absolute inset-0 bg-gradient-to-t from-black/70 via-black/30 to-transparent flex items-center justify-center opacity-0"
        >
          <svg 
            ref={playIconRef}
            className="w-16 h-16 text-white drop-shadow-lg" 
            fill="currentColor" 
            viewBox="0 0 20 20"
          >
            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm0-2a6 6 0 100-12 6 6 0 000 12zm-1.09-7.59a.5.5 0 01.09-.7L11.5 5.5a.5.5 0 01.78.41v7.18a.5.5 0 01-.78.41l-2.5-2.21a.5.5 0 01-.09-.7z" clipRule="evenodd" />
          </svg>
          <div className="absolute bottom-4 left-4 right-4">
            <h3 className="text-white font-semibold text-lg mb-1">{project.title}</h3>
            <p className="text-gray-200 text-sm">{project.duration}</p>
          </div>
        </div>
      )}
    </div>
  );
};

// Studio Noir style portfolio item with GSAP animations
const StudioNoirPortfolioItem = ({ project, showDescription, index }: { project: any, showDescription: boolean, index: number }) => {
  const itemRef = useRef<HTMLDivElement>(null);
  const imageRef = useRef<HTMLDivElement>(null);
  const overlayRef = useRef<HTMLDivElement>(null);
  const titleRef = useRef<HTMLHeadingElement>(null);
  const descriptionRef = useRef<HTMLParagraphElement>(null);

  useEffect(() => {
    const ctx = gsap.context(() => {
      // Initial state
      gsap.set(itemRef.current, { opacity: 0, y: 60, rotationX: 15 });
      
      // Entrance animation
      gsap.to(itemRef.current, {
        opacity: 1,
        y: 0,
        rotationX: 0,
        duration: 0.8,
        delay: index * 0.15,
        ease: "power3.out",
        scrollTrigger: {
          trigger: itemRef.current,
          start: "top 85%",
          toggleActions: "play none none reverse"
        }
      });

      // Hover animations
      const handleMouseEnter = () => {
        gsap.to(itemRef.current, {
          y: -10,
          scale: 1.02,
          duration: 0.4,
          ease: "power2.out"
        });
        
        gsap.to(imageRef.current, {
          scale: 1.1,
          duration: 0.6,
          ease: "power2.out"
        });
        
        if (overlayRef.current) {
          gsap.to(overlayRef.current, {
            opacity: 1,
            duration: 0.3,
            ease: "power2.out"
          });
        }
        
        if (titleRef.current) {
          gsap.to(titleRef.current, {
            color: "#60A5FA",
            duration: 0.3,
            ease: "power2.out"
          });
        }
        
        if (showDescription && descriptionRef.current) {
          gsap.to(descriptionRef.current, {
            height: "auto",
            opacity: 1,
            duration: 0.4,
            ease: "power2.out"
          });
        }
      };

      const handleMouseLeave = () => {
        gsap.to(itemRef.current, {
          y: 0,
          scale: 1,
          duration: 0.4,
          ease: "power2.out"
        });
        
        gsap.to(imageRef.current, {
          scale: 1,
          duration: 0.6,
          ease: "power2.out"
        });
        
        if (overlayRef.current) {
          gsap.to(overlayRef.current, {
            opacity: 0,
            duration: 0.3,
            ease: "power2.out"
          });
        }
        
        if (titleRef.current) {
          gsap.to(titleRef.current, {
            color: "#FFFFFF",
            duration: 0.3,
            ease: "power2.out"
          });
        }
        
        if (showDescription && descriptionRef.current) {
          gsap.to(descriptionRef.current, {
            height: "2.5rem",
            opacity: 0.7,
            duration: 0.4,
            ease: "power2.out"
          });
        }
      };

      const element = itemRef.current;
      if (element) {
        element.addEventListener('mouseenter', handleMouseEnter);
        element.addEventListener('mouseleave', handleMouseLeave);
        
        return () => {
          element.removeEventListener('mouseenter', handleMouseEnter);
          element.removeEventListener('mouseleave', handleMouseLeave);
        };
      }
    }, itemRef);

    return () => ctx.revert();
  }, [index, showDescription]);

  return (
    <div ref={itemRef} className="bg-gradient-to-br from-gray-800 to-gray-900 rounded-lg overflow-hidden cursor-pointer transform-gpu shadow-lg hover:shadow-2xl transition-shadow duration-300">
      <div className="relative w-full h-48 overflow-hidden">
        <div ref={imageRef} className="w-full h-full">
          <Image src={project.imageUrl} alt={project.title} layout="fill" objectFit="cover" />
        </div>
        <div 
          ref={overlayRef}
          className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/40 to-transparent flex items-center justify-center opacity-0"
        >
          <svg className="w-12 h-12 text-white animate-pulse" fill="currentColor" viewBox="0 0 20 20">
            <path d="M10 18a8 8 0 100-16 8 8 0 000 16zm0-2a6 6 0 100-12 6 6 0 000 12zm-1.09-7.59a.5.5 0 01.09-.7L11.5 5.5a.5.5 0 01.78.41v7.18a.5.5 0 01-.78.41l-2.5-2.21a.5.5 0 01-.09-.7z"></path>
          </svg>
        </div>
        <span className="absolute bottom-2 right-2 bg-black/75 text-white text-xs px-2 py-1 rounded backdrop-blur-sm">
          {project.duration}
        </span>
      </div>
      <div className="p-4">
        <h3 ref={titleRef} className="text-lg font-semibold text-white mb-2 transition-colors duration-300">
          {project.title}
        </h3>
        {showDescription && (
          <p 
            ref={descriptionRef}
            className="text-sm text-gray-400 overflow-hidden transition-all duration-300"
            style={{ height: '2.5rem', opacity: 0.7 }}
          >
            {project.description}
          </p>
        )}
      </div>
    </div>
  );
};

const projectCategories = [
  { title: 'Commercial Video Editing', descriptionToggle: true, projects: allProjects.filter(p => p.category === 'Commercial Video Editing' && p.type === 'studionoir') },
  { title: 'Short Film Editing', descriptionToggle: true, projects: allProjects.filter(p => p.category === 'Short Film Editing' && p.type === 'studionoir') },
  { title: 'Music Video Editing', descriptionToggle: true, projects: allProjects.filter(p => p.category === 'Music Video Editing' && p.type === 'studionoir') },
  { title: 'AMVs', descriptionToggle: true, projects: allProjects.filter(p => p.category === 'AMVs' && p.type === 'studionoir') },
  { title: 'WIS Edits', descriptionToggle: true, projects: allProjects.filter(p => p.category === 'WIS Edits' && p.type === 'studionoir') },
  { title: 'Product Commercials (Studio)', descriptionToggle: true, projects: allProjects.filter(p => p.category === 'Product Commercials' && p.type === 'studionoir') }, // Differentiate from VISUALS if needed
  { title: 'Podcast-style Edits', descriptionToggle: true, projects: allProjects.filter(p => p.category === 'Podcast-style Edits' && p.type === 'studionoir') },
  { title: 'YouTube-specific Edits', descriptionToggle: true, projects: allProjects.filter(p => p.category === 'YouTube-specific Edits' && p.type === 'studionoir') },
  { title: 'Other Creative Works (Studio)', descriptionToggle: true, projects: allProjects.filter(p => p.category === 'Other Creative Works' && p.type === 'studionoir') },
];

export default function PortfolioPage() {
  const [activeFilter, setActiveFilter] = useState('All Projects');
  const [visibleProjects, setVisibleProjects] = useState(8); // Initial number of VISUALS items
  const [categoryDescriptions, setCategoryDescriptions] = useState<Record<string, boolean>>({});
  
  // Refs for GSAP animations
  const heroRef = useRef<HTMLDivElement>(null);
  const heroContentRef = useRef<HTMLDivElement>(null);
  const filterBarRef = useRef<HTMLDivElement>(null);
  const portfolioGridRef = useRef<HTMLDivElement>(null);
  const loadMoreRef = useRef<HTMLButtonElement>(null);

  const filteredVisualsProjects = useMemo(() => {
    const visualsStyleProjects = allProjects.filter(p => p.type === 'visuals');
    if (activeFilter === 'All Projects') return visualsStyleProjects;
    return visualsStyleProjects.filter(project => project.category === activeFilter);
  }, [activeFilter]);

  const toggleDescription = (categoryTitle: string) => {
    setCategoryDescriptions(prev => ({ ...prev, [categoryTitle]: !prev[categoryTitle] }));
  };

  const loadMoreVisuals = () => {
    setVisibleProjects(prev => prev + 4); // Load 4 more visuals items
  };

  // GSAP animations
  useEffect(() => {
    const ctx = gsap.context(() => {
      // Hero section animations
      const heroTl = gsap.timeline();
      
      heroTl
        .fromTo(heroContentRef.current?.children[0], 
          { scale: 0.8, opacity: 0, rotationY: 15 },
          { scale: 1, opacity: 1, rotationY: 0, duration: 1.2, ease: "power3.out" }
        )
        .fromTo(heroContentRef.current?.children[1], 
          { y: 50, opacity: 0 },
          { y: 0, opacity: 1, duration: 0.8, ease: "power2.out" },
          "-=0.6"
        )
        .fromTo(heroContentRef.current?.children[2], 
          { y: 30, opacity: 0 },
          { y: 0, opacity: 1, duration: 0.6, ease: "power2.out" },
          "-=0.4"
        );

      // Filter bar animation
      gsap.fromTo(filterBarRef.current?.children[0]?.children || [], 
        { y: -20, opacity: 0, scale: 0.9 },
        {
          y: 0,
          opacity: 1,
          scale: 1,
          duration: 0.5,
          stagger: 0.05,
          ease: "back.out(1.7)",
          delay: 0.8
        }
      );

      // Load more button animation
      if (loadMoreRef.current) {
        gsap.fromTo(loadMoreRef.current, 
          { scale: 0, rotation: 180 },
          {
            scale: 1,
            rotation: 0,
            duration: 0.6,
            ease: "back.out(1.7)",
            scrollTrigger: {
              trigger: loadMoreRef.current,
              start: "top 90%",
              toggleActions: "play none none reverse"
            }
          }
        );
      }

      // Parallax effect for hero background
      gsap.to(heroRef.current, {
        backgroundPosition: "50% 100px",
        ease: "none",
        scrollTrigger: {
          trigger: heroRef.current,
          start: "top bottom",
          end: "bottom top",
          scrub: true
        }
      });

    }, heroRef);

    return () => ctx.revert();
  }, []);

  // Filter change animation
  useEffect(() => {
    if (portfolioGridRef.current) {
      gsap.fromTo(portfolioGridRef.current.children, 
        { opacity: 0, y: 20, scale: 0.95 },
        {
          opacity: 1,
          y: 0,
          scale: 1,
          duration: 0.6,
          stagger: 0.1,
          ease: "power2.out"
        }
      );
    }
  }, [activeFilter, visibleProjects]);

  return (
    <div className="bg-gray-900 text-white min-h-screen">
      {/* Hero Section - VISUALS Style */}
      <section 
        ref={heroRef}
        className="relative h-[70vh] flex items-center justify-center text-center bg-cover bg-center overflow-hidden"
        style={{ backgroundImage: "url('/hero-portfolio-bg.svg')" }}
      >
        <div className="absolute inset-0 bg-gradient-to-b from-black/60 via-black/50 to-black/70"></div>
        
        {/* Animated background particles */}
        <div className="absolute inset-0">
          <div className="absolute top-1/4 left-1/4 w-3 h-3 bg-brand-purple rounded-full opacity-60 animate-pulse"></div>
          <div className="absolute top-3/4 right-1/3 w-2 h-2 bg-brand-yellow rounded-full opacity-70 animate-ping"></div>
          <div className="absolute top-1/2 left-3/4 w-4 h-4 bg-blue-500 rounded-full opacity-50 animate-bounce"></div>
          <div className="absolute bottom-1/4 left-1/2 w-2 h-2 bg-brand-purple rounded-full opacity-40 animate-pulse"></div>
        </div>
        
        <div ref={heroContentRef} className="relative z-10 p-4">
          {/* Centered Image/Video Player Element */}
          <div className="mb-8 mx-auto max-w-3xl h-64 md:h-96 bg-gradient-to-br from-gray-800 to-gray-900 rounded-lg shadow-2xl overflow-hidden border border-gray-700">
            <Image src="/hero-portfolio-bg.svg" alt="Elevating Stories Through Video" layout="fill" objectFit="cover" className="hover:scale-105 transition-transform duration-700" />
          </div>
          <h1 className="text-5xl md:text-7xl font-bold mb-4 bg-gradient-to-r from-white via-blue-400 to-brand-purple bg-clip-text text-transparent">
            Elevating Stories Through Video
          </h1>
          <p className="text-xl md:text-2xl text-gray-300 mb-8 max-w-3xl mx-auto leading-relaxed">
            Explore a diverse portfolio of video editing projects, demonstrating expertise in various styles and client needs.
          </p>
        </div>
      </section>

      {/* Filter Bar - VISUALS Style */}
      <section ref={filterBarRef} className="py-8 sticky top-0 z-20 bg-gray-900/90 backdrop-blur-md border-b border-gray-800">
        <div className="container mx-auto px-4 flex justify-center space-x-2 md:space-x-4 overflow-x-auto pb-2">
          {filters.map((filter, index) => (
            <button
              key={filter}
              onClick={() => setActiveFilter(filter)}
              className={`px-4 py-2 text-sm md:text-base rounded-md transition-all duration-300 whitespace-nowrap transform hover:scale-105 hover:-translate-y-1
                          ${activeFilter === filter 
                            ? 'bg-gradient-to-r from-blue-600 to-brand-purple text-white shadow-lg shadow-blue-500/25' 
                            : 'bg-gray-700 hover:bg-gradient-to-r hover:from-gray-600 hover:to-gray-500 text-gray-300 hover:text-white'}`}
            >
              {filter}
            </button>
          ))}
        </div>
      </section>

      {/* Portfolio Grid - VISUALS Style (Top Section) */}
      {(activeFilter === 'All Projects' || allProjects.some(p => p.category === activeFilter && p.type === 'visuals')) && (
        <section className="py-12">
          <div className="container mx-auto px-4">
            <div ref={portfolioGridRef} className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 md:gap-6">
              {filteredVisualsProjects.slice(0, visibleProjects).map((project, index) => (
                <VisualsPortfolioItem key={project.id} project={project} index={index} />
              ))}
            </div>
            {visibleProjects < filteredVisualsProjects.length && (
              <div className="text-center mt-12">
                <button 
                  ref={loadMoreRef}
                  onClick={loadMoreVisuals}
                  className="group bg-gradient-to-r from-blue-600 to-brand-purple hover:from-blue-700 hover:to-brand-purple text-white font-semibold py-3 px-8 rounded-lg transition-all duration-300 text-lg flex items-center justify-center mx-auto transform hover:scale-105 shadow-lg hover:shadow-xl"
                >
                  Load More 
                  <svg className="w-5 h-5 ml-2 group-hover:animate-bounce" fill="none" strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" viewBox="0 0 24 24" stroke="currentColor">
                    <path d="M19 9l-7 7-7-7"></path>
                  </svg>
                </button>
              </div>
            )}
          </div>
        </section>
      )}

      {/* Categorized Projects - Studio Noir Style */}
      <section className="py-12 bg-gray-900">
        <div className="container mx-auto px-4">
          {projectCategories.map(category => {
            const projectsForCategory = (activeFilter === 'All Projects' || activeFilter === category.title) 
                                        ? category.projects 
                                        : category.projects.filter(p => p.category === activeFilter);
            
            if (projectsForCategory.length === 0 && activeFilter !== 'All Projects' && activeFilter !== category.title) return null; // Hide section if filter doesn't match and no projects
            if (category.projects.length === 0 && activeFilter === 'All Projects') return null; // Hide if no projects for this category at all when 'All Projects' is selected

            // Only render category if it's the active filter, or if 'All Projects' is selected and it has projects
            if (activeFilter !== 'All Projects' && activeFilter !== category.title && !allProjects.some(p => p.category === activeFilter && p.type === 'studionoir')) {
                // If a VISUALS category is selected, don't show Studio Noir sections unless they match
                if (filters.slice(0,7).includes(activeFilter) && !category.projects.some(p => p.category === activeFilter)) return null;
            }

            return (
              <div key={category.title} className="mb-16">
                <div className="flex justify-between items-center mb-6">
                  <h2 className="text-3xl font-semibold text-white">{category.title}</h2>
                  {category.descriptionToggle && (
                    <button 
                      onClick={() => toggleDescription(category.title)}
                      className="text-sm text-gray-400 hover:text-blue-400 transition-colors"
                    >
                      Show Description {categoryDescriptions[category.title] ? '▲' : '▼'}
                    </button>
                  )}
                </div>
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8">
                  {projectsForCategory.map((project, index) => (
                    <StudioNoirPortfolioItem 
                      key={project.id} 
                      project={project} 
                      showDescription={!!categoryDescriptions[category.title]} 
                      index={index}
                    />
                  ))}
                </div>
              </div>
            );
          })}
        </div>
      </section>

      {/* Footer - Consistent with VISUALS Style */}
      <footer className="py-8 text-center border-t border-gray-700">
        <p className="text-gray-400 text-sm">
          &copy; {new Date().getFullYear()} VISUALS. All rights reserved. Crafted with passion.
        </p>
        <div className="flex justify-center space-x-4 mt-4">
          <Link href="#" legacyBehavior><a className="text-gray-400 hover:text-white"><svg className="w-6 h-6" fill="currentColor" viewBox="0 0 24 24"><path d="M22.46 6c-.77.35-1.6.58-*********-.53 1.59-1.37 1.92-2.38-.84.5-1.77.86-2.76 1.05C18.37 4.5 17.26 4 16 4c-2.35 0-4.27 1.92-4.27 4.29 0 .***********.98C8.28 9.09 5.11 7.38 3 4.79c-.37.63-.58 1.37-.58 2.15 0 1.49.75 2.81 1.91 3.56-.71 0-1.37-.22-1.95-.5v.03c0 2.08 1.48 3.82 3.44 4.21a4.22 4.22 0 0 1-1.94.07 4.28 4.28 0 0 0 4 2.96 8.61 8.61 0 0 1-5.33 1.84c-.34 0-.68-.02-1.01-.06C2.64 19.41 5.08 20 7.74 20c7.28 0 11.99-6.08 11.99-12.25 0-.19 0-.37-.01-.56.77-.57 1.44-1.28 1.97-2.02z"></path></svg></a></Link>
          <Link href="#" legacyBehavior><a className="text-gray-400 hover:text-white"><svg className="w-6 h-6" fill="currentColor" viewBox="0 0 24 24"><path fillRule="evenodd" d="M12 2C6.477 2 2 6.477 2 12c0 4.418 2.865 8.166 6.737 9.492.5.092.682-.217.682-.483 0-.237-.009-.868-.013-1.703-2.782.605-3.369-1.343-3.369-1.343-.454-1.158-1.11-1.466-1.11-1.466-.908-.62.069-.608.069-.608 1.003.07 1.531 1.032 1.531 1.032.892 1.53 2.341 1.088 2.91.832.092-.647.35-1.088.636-1.338-2.22-.253-4.555-1.113-4.555-4.951 0-1.093.39-1.988 1.029-2.688-.103-.253-.446-1.272.098-2.65 0 0 .84-.27 2.75 1.026A9.564 9.564 0 0112 6.844c.85.004 1.705.115 2.504.337 1.909-1.296 2.747-1.026 2.747-1.026.546 1.378.201 2.397.098 ********* 1.028 1.595 1.028 2.688 0 3.848-2.339 4.695-4.566 4.942.359.309.678.92.678 1.855 0 1.338-.012 2.419-.012 2.747 0 .268.18.58.688.482A10.001 10.001 0 0022 12c0-5.523-4.477-10-10-10z" clipRule="evenodd"></path></svg></a></Link>
          <Link href="#" legacyBehavior><a className="text-gray-400 hover:text-white"><svg className="w-6 h-6" fill="currentColor" viewBox="0 0 24 24"><path d="M12 2.03037C6.49243 2.03037 2.03037 6.49243 2.03037 12C2.03037 17.5076 6.49243 21.9696 12 21.9696C17.5076 21.9696 21.9696 17.5076 21.9696 12C21.9696 6.49243 17.5076 2.03037 12 2.03037ZM12 20.0908C7.53687 20.0908 3.90924 16.4632 3.90924 12C3.90924 7.53687 7.53687 3.90924 12 3.90924C16.4632 3.90924 20.0908 7.53687 20.0908 12C20.0908 16.4632 16.4632 20.0908 12 20.0908ZM12.9393 12.9393L12.9393 17.2727C12.9393 17.7955 12.5121 18.2227 11.9999 18.2227C11.4878 18.2227 11.0605 17.7955 11.0605 17.2727L11.0605 12.9393L6.72721 12.9393C6.20448 12.9393 5.77721 12.5121 5.77721 11.9999C5.77721 11.4878 6.20448 11.0605 6.72721 11.0605L11.0605 11.0605L11.0605 6.72721C11.0605 6.20448 11.4878 5.77721 11.9999 5.77721C12.5121 5.77721 12.9393 6.20448 12.9393 6.72721L12.9393 11.0605L17.2727 11.0605C17.7955 11.0605 18.2227 11.4878 18.2227 11.9999C18.2227 12.5121 17.7955 12.9393 17.2727 12.9393L12.9393 12.9393Z"></path></svg></a></Link>
        </div>
      </footer>
    </div>
  );
}