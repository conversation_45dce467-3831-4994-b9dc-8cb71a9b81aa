'use client';

import Link from 'next/link';
import { useEffect, useRef, useState } from 'react';
import { gsap } from 'gsap';
import { usePathname } from 'next/navigation';

const Header = () => {
  const headerRef = useRef<HTMLElement>(null);
  const logoRef = useRef<HTMLAnchorElement>(null);
  const navLinksRef = useRef<HTMLDivElement>(null);
  const ctaButtonRef = useRef<HTMLAnchorElement>(null);
  const [isScrolled, setIsScrolled] = useState(false);
  const pathname = usePathname();

  // Navigation items
  const navItems = [
    { href: '/', label: 'Home' },
    { href: '/about', label: 'About' },
    { href: '/services', label: 'Services' },
    { href: '/portfolio', label: 'Portfolio' },
    { href: '/contact', label: 'Contact' }
  ];

  useEffect(() => {
    const ctx = gsap.context(() => {
      // Initial entrance animation
      const tl = gsap.timeline();
      
      // Header slide down
      tl.fromTo(headerRef.current, 
        { y: -100, opacity: 0 },
        { y: 0, opacity: 1, duration: 0.8, ease: "power3.out" }
      )
      // Logo animation
      .fromTo(logoRef.current, 
        { scale: 0, rotation: -180 },
        { scale: 1, rotation: 0, duration: 0.6, ease: "back.out(1.7)" },
        "-=0.4"
      )
      // Navigation links stagger
      .fromTo(navLinksRef.current?.children || [], 
        { y: -20, opacity: 0 },
        { y: 0, opacity: 1, duration: 0.4, stagger: 0.1, ease: "power2.out" },
        "-=0.3"
      )
      // CTA button
      .fromTo(ctaButtonRef.current, 
        { scale: 0, opacity: 0 },
        { scale: 1, opacity: 1, duration: 0.5, ease: "back.out(1.7)" },
        "-=0.2"
      );

      // Logo hover animation
      const handleLogoHover = () => {
        gsap.to(logoRef.current, {
          scale: 1.1,
          rotation: 5,
          duration: 0.3,
          ease: "power2.out"
        });
      };

      const handleLogoLeave = () => {
        gsap.to(logoRef.current, {
          scale: 1,
          rotation: 0,
          duration: 0.3,
          ease: "power2.out"
        });
      };

      // CTA button hover animation
      const handleCtaHover = () => {
        gsap.to(ctaButtonRef.current, {
          scale: 1.05,
          y: -2,
          duration: 0.3,
          ease: "power2.out"
        });
      };

      const handleCtaLeave = () => {
        gsap.to(ctaButtonRef.current, {
          scale: 1,
          y: 0,
          duration: 0.3,
          ease: "power2.out"
        });
      };

      // Add event listeners
      const logo = logoRef.current;
      const ctaButton = ctaButtonRef.current;
      
      if (logo) {
        logo.addEventListener('mouseenter', handleLogoHover);
        logo.addEventListener('mouseleave', handleLogoLeave);
      }
      
      if (ctaButton) {
        ctaButton.addEventListener('mouseenter', handleCtaHover);
        ctaButton.addEventListener('mouseleave', handleCtaLeave);
      }

      return () => {
        if (logo) {
          logo.removeEventListener('mouseenter', handleLogoHover);
          logo.removeEventListener('mouseleave', handleLogoLeave);
        }
        if (ctaButton) {
          ctaButton.removeEventListener('mouseenter', handleCtaHover);
          ctaButton.removeEventListener('mouseleave', handleCtaLeave);
        }
      };
    }, headerRef);

    return () => ctx.revert();
  }, []);

  // Scroll effect
  useEffect(() => {
    const handleScroll = () => {
      const scrolled = window.scrollY > 50;
      setIsScrolled(scrolled);
      
      if (scrolled && !isScrolled) {
        gsap.to(headerRef.current, {
          backgroundColor: "rgba(0, 0, 0, 0.95)",
          backdropFilter: "blur(10px)",
          duration: 0.3,
          ease: "power2.out"
        });
      } else if (!scrolled && isScrolled) {
        gsap.to(headerRef.current, {
          backgroundColor: "rgba(0, 0, 0, 1)",
          backdropFilter: "blur(0px)",
          duration: 0.3,
          ease: "power2.out"
        });
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, [isScrolled]);

  return (
    <header 
      ref={headerRef}
      className={`sticky top-0 z-50 transition-all duration-300 ${
        isScrolled 
          ? 'bg-black/95 backdrop-blur-md shadow-2xl border-b border-gray-800' 
          : 'bg-black shadow-lg'
      }`}
    >
      <nav className="container mx-auto px-6 py-4 flex justify-between items-center">
        {/* Logo */}
        <Link 
          ref={logoRef}
          href="/" 
          className="text-2xl font-bold bg-gradient-to-r from-brand-yellow to-yellow-300 bg-clip-text text-transparent hover:from-yellow-300 hover:to-brand-yellow transition-all duration-300 cursor-pointer"
        >
          Studio Noir
        </Link>
        
        {/* Navigation Links */}
        <div ref={navLinksRef} className="hidden md:flex space-x-8">
          {navItems.map((item, index) => {
            const isActive = pathname === item.href;
            return (
              <NavLink 
                key={item.href}
                href={item.href}
                isActive={isActive}
                index={index}
              >
                {item.label}
              </NavLink>
            );
          })}
        </div>
        
        {/* CTA Button */}
        <Link 
          href="/contact" 
          ref={ctaButtonRef}
          className="bg-gradient-to-r from-brand-purple to-purple-600 hover:from-purple-600 hover:to-brand-purple text-white font-semibold py-2 px-6 rounded-lg transition-all duration-300 shadow-lg hover:shadow-xl transform-gpu cursor-pointer"
        >
          Get a Quote
        </Link>
      </nav>
    </header>
  );
};

// Individual navigation link component with animations
const NavLink = ({ href, children, isActive, index }: {
  href: string;
  children: React.ReactNode;
  isActive: boolean;
  index: number;
}) => {
  const linkRef = useRef<HTMLAnchorElement>(null);
  const underlineRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const ctx = gsap.context(() => {
      // Set initial underline state
      gsap.set(underlineRef.current, {
        scaleX: isActive ? 1 : 0,
        transformOrigin: "left center"
      });

      const handleMouseEnter = () => {
        gsap.to(linkRef.current, {
          y: -2,
          color: "#FFD700",
          duration: 0.3,
          ease: "power2.out"
        });
        
        gsap.to(underlineRef.current, {
          scaleX: 1,
          duration: 0.3,
          ease: "power2.out"
        });
      };

      const handleMouseLeave = () => {
        gsap.to(linkRef.current, {
          y: 0,
          color: isActive ? "#FFD700" : "#FFFFFF",
          duration: 0.3,
          ease: "power2.out"
        });
        
        if (!isActive) {
          gsap.to(underlineRef.current, {
            scaleX: 0,
            duration: 0.3,
            ease: "power2.out"
          });
        }
      };

      const link = linkRef.current;
      if (link) {
        link.addEventListener('mouseenter', handleMouseEnter);
        link.addEventListener('mouseleave', handleMouseLeave);
        
        return () => {
          link.removeEventListener('mouseenter', handleMouseEnter);
          link.removeEventListener('mouseleave', handleMouseLeave);
        };
      }
    }, linkRef);

    return () => ctx.revert();
  }, [isActive]);

  return (
    <Link 
      href={href} 
      ref={linkRef}
      className={`relative font-medium transition-all duration-300 transform-gpu ${
        isActive 
          ? 'text-brand-yellow' 
          : 'text-white hover:text-brand-yellow'
      }`}
    >
      {children}
      <div 
        ref={underlineRef}
        className="absolute bottom-0 left-0 w-full h-0.5 bg-gradient-to-r from-brand-yellow to-yellow-300 transform scale-x-0 origin-left"
      />
    </Link>
  );
};

export default Header;